{"name": "srvxp-appointment-bot", "version": "1.0.0", "description": "Automated appointment booking bot for Quebec health services using Puppeteer", "main": "SRVXP_Bot.js", "scripts": {"start": "node \"SRVXP_Bot.js\"", "dev": "node \"SRVXP_Bot.js\"", "install-deps": "npm install"}, "keywords": ["puppeteer", "automation", "appointment", "bot", "quebec", "health", "booking"], "author": "Anonymous", "license": "MIT", "dependencies": {"puppeteer": "^24.10.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "."}, "private": true}