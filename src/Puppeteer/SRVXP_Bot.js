const puppeteer = require('puppeteer');

// ========================================
// USER CONFIGURATION SECTION
// ========================================
// Update these variables with your personal information
const USER_CONFIG = {
  // Personal Information (for identification form)
  firstName: 'Manu',
  lastName: 'Jette',
  healthCardNumber: 'JETM21081817',  // NAM (Numéro d'assurance maladie)
  cardSequenceNumber: '01',          // Card sequence number
  birthDay: '18',                    // Birth day (DD)
  birthMonth: '08',                  // Birth month (MM)
  birthYear: '2021',                 // Birth year (YYYY)
  email: '<EMAIL>',                     // email
  cellphone: '************',                     // email
  language: 'french',                     // french or english
  communicationMethod: 'email',            // email, sms, or both
  
  // Location and Search Preferences
  postalCode: 'H2W1A3',             // Your postal code
  searchPerimeter: '5',              // Search radius: 1=10km, 2=20km, 3=30km, 4=40km, 5=50km
  
  // Appointment Preferences
  appointmentDate: '12-06-2025',     // Desired appointment date (DD-MM-YYYY)
  
  // Consultation Type
  consultationReason: 'ac2a5fa4-8514-11ef-a759-005056b11d6c', // Consultation urgente
  
  // ========================================
  // ADDITIONAL CONFIGURATION SETTINGS
  // ========================================
  
  // Time Preferences (FUTURE FEATURE)
  momentOfDay: 'asap',               // Options: 'morning', 'afternoon', 'evening', 'asap'
                                     // Will be used to filter time slots by preferred time of day
  
  // Transportation Distance
  transportationDistance: '50',   // Options: 5, 10, 20, 30, 40 and 50 km are the available options
                                     // Maximum distance willing to travel to a clinic (default: 10 km)
  afterThisTime: '8:00',      // Only accept appointments after this time (format: HH:MM, e.g., '9:00', '13:30'). Leave empty '' for no constraint.
  beforeThisTime: '17:30',      // Only accept appointments before this time (format: HH:MM, e.g., '17:00', '21:30'). Leave empty '' for no constraint.
  notBeforeThisDelay: '0'        // Minimum hours before appointment (applies to today's appointments only, '0' = No delay, '1' = 1-hour minimum delay, '2' = 2-hour minimum delay (default) )
};

// Validation function to ensure all required fields are provided
function validateUserConfig() {
  const requiredFields = [
    'firstName', 'lastName', 'healthCardNumber', 'cardSequenceNumber',
    'birthDay', 'birthMonth', 'birthYear', 'postalCode', 'searchPerimeter', 'appointmentDate',
    'notBeforeThisDelay', 'transportationDistance', 'email', 'cellphone', 'language'
  ];
  
  // Note: momentOfDay is a future feature and not validated yet
  
  for (const field of requiredFields) {
    if (!USER_CONFIG[field] || (typeof USER_CONFIG[field] === 'string' && USER_CONFIG[field].trim().length === 0)) {
      throw new Error(`USER_CONFIG.${field} is required but not provided or is empty`);
    }
  }
  
  // Additional validation for notBeforeThisDelay
  const delayValue = parseInt(USER_CONFIG.notBeforeThisDelay);
  if (isNaN(delayValue) || delayValue < 0 || delayValue > 24) {
    throw new Error(`USER_CONFIG.notBeforeThisDelay must be a number between 0 and 24 hours. Current value: ${USER_CONFIG.notBeforeThisDelay}`);
  }
  
  // Additional validation for transportationDistance
  const allowedDistances = ['5', '10', '20', '30', '40', '50'];
  if (!allowedDistances.includes(USER_CONFIG.transportationDistance)) {
    throw new Error(`USER_CONFIG.transportationDistance must be one of: ${allowedDistances.join(', ')} km. Current value: ${USER_CONFIG.transportationDistance}`);
  }
  
  // Additional validation for language
  const allowedLanguages = ['french', 'english'];
  if (!allowedLanguages.includes(USER_CONFIG.language.toLowerCase())) {
    throw new Error(`USER_CONFIG.language must be one of: ${allowedLanguages.join(', ')}. Current value: ${USER_CONFIG.language}`);
  }
  
  // Additional validation for email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(USER_CONFIG.email)) {
    throw new Error(`USER_CONFIG.email must be a valid email address. Current value: ${USER_CONFIG.email}`);
  }
  
  // Validation for beforeThisTime and afterThisTime (optional fields)
  function validateTimeFormat(timeString, fieldName) {
    if (!timeString) return true; // Optional field
    
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;
    if (!timeRegex.test(timeString)) {
      throw new Error(`USER_CONFIG.${fieldName} must be in format HH:MM (e.g., 9:00, 17:30). Current value: ${timeString}`);
    }
    return true;
  }
  
  validateTimeFormat(USER_CONFIG.beforeThisTime, 'beforeThisTime');
  validateTimeFormat(USER_CONFIG.afterThisTime, 'afterThisTime');
  
  // Validate that afterThisTime is before beforeThisTime if both are provided
  if (USER_CONFIG.afterThisTime && USER_CONFIG.beforeThisTime) {
    const afterParts = USER_CONFIG.afterThisTime.split(':');
    const beforeParts = USER_CONFIG.beforeThisTime.split(':');
    
    const afterMinutes = parseInt(afterParts[0]) * 60 + parseInt(afterParts[1]);
    const beforeMinutes = parseInt(beforeParts[0]) * 60 + parseInt(beforeParts[1]);
    
    if (afterMinutes >= beforeMinutes) {
      throw new Error(`USER_CONFIG.afterThisTime (${USER_CONFIG.afterThisTime}) must be earlier than USER_CONFIG.beforeThisTime (${USER_CONFIG.beforeThisTime})`);
    }
  }
  
  console.log('User configuration validated successfully');
  console.log(`Booking appointment for: ${USER_CONFIG.firstName} ${USER_CONFIG.lastName}`);
  console.log(`Health Card: ${USER_CONFIG.healthCardNumber}`);
  console.log(`Target Date: ${USER_CONFIG.appointmentDate}`);
  console.log(`Postal Code: ${USER_CONFIG.postalCode}`);
  console.log(`Maximum transportation distance: ${USER_CONFIG.transportationDistance} km`);
  console.log(`Minimum delay for today's appointments: ${USER_CONFIG.notBeforeThisDelay} hours`);
  console.log(`Contact Email: ${USER_CONFIG.email}`);
  console.log(`Contact Phone: ${USER_CONFIG.cellphone}`);
  console.log(`Communication Language: ${USER_CONFIG.language}`);
  if (USER_CONFIG.afterThisTime) {
    console.log(`Time preference: After ${USER_CONFIG.afterThisTime}`);
  }
  if (USER_CONFIG.beforeThisTime) {
    console.log(`Time preference: Before ${USER_CONFIG.beforeThisTime}`);
  }
}

// ========================================
// MAIN EXECUTION FUNCTION
// ========================================

// Main execution function
(function() {
  // Validate user configuration before starting
  try {
    validateUserConfig();
  } catch (error) {
    console.error('Configuration Error:', error.message);
    return;
  }

  // Launch the browser
  puppeteer.launch({
    headless: false,  // Set to true for headless mode, false to see the browser
    defaultViewport: null,  // Use default viewport size of the browser
    args: ['--start-maximized']  // Start with maximized browser window
  }).then(function(browser) {
    // Create a new page
    return browser.newPage().then(function(page) {
      // Track clinics we've already visited to avoid duplicates
      const visitedClinics = new Set();

      // Define a function to wait for page load by looking for the "Nous joindre" link
      function waitForPageLoad(page, timeout) {
        timeout = timeout || 30000;
        return page.waitForSelector('a[href="../Accueil/nousjoindre.html"]', { 
          visible: true, 
          timeout: timeout 
        }).then(function() {
          console.log('Page loaded successfully (Nous joindre link visible)');
        }).catch(function(e) {
          console.log('Timed out waiting for "Nous joindre" link, continuing anyway...');
        });
      }

      /**
       * Function to check for and handle doctor selection page
       * Returns true if doctor selection was handled, false if not needed
       */
      function handleDoctorSelection(page) {
        console.log("=== DOCTOR SELECTION DEBUG: Starting doctor selection check ===");
        
        // Wait for the page to load properly using the existing waitForPageLoad function
        return waitForPageLoad(page).then(function() {
          console.log("=== DOCTOR SELECTION DEBUG: Page loaded, checking for doctor selection elements ===");
          
          // Check if the doctor selection page is present
          return page.evaluate(function() {
            console.log("=== DOCTOR SELECTION DEBUG: Inside page.evaluate ===");
            
            // Look for the signal element that indicates doctor selection is needed
            const h2Elements = document.querySelectorAll('h2');
            console.log("=== DOCTOR SELECTION DEBUG: Found", h2Elements.length, "h2 elements ===");
            
            let signalFound = false;
            for (let i = 0; i < h2Elements.length; i++) {
              const text = h2Elements[i].textContent || h2Elements[i].innerText || '';
              console.log("=== DOCTOR SELECTION DEBUG: H2 element", i, "text:", text.trim());
              if (text.includes('Avec quel professionnel de la santé')) {
                console.log("=== DOCTOR SELECTION DEBUG: Signal element found! ===");
                signalFound = true;
                break;
              }
            }
            
            if (!signalFound) {
              console.log("=== DOCTOR SELECTION DEBUG: No signal element found, checking page content ===");
              const bodyText = document.body.textContent || document.body.innerText || '';
              console.log("=== DOCTOR SELECTION DEBUG: Page contains 'Avec quel professionnel':", bodyText.includes('Avec quel professionnel'));
              console.log("=== DOCTOR SELECTION DEBUG: Page title:", document.title);
              console.log("=== DOCTOR SELECTION DEBUG: Current URL:", window.location.href);
              return { needsDoctorSelection: false, reason: "Signal element not found" };
            }
            
            // Look for doctor selection elements
            const doctorElements = document.querySelectorAll('.h-selectProfessional');
            console.log("=== DOCTOR SELECTION DEBUG: Found", doctorElements.length, "doctor selection elements ===");
            
            if (doctorElements.length === 0) {
              console.log("=== DOCTOR SELECTION DEBUG: No .h-selectProfessional elements found ===");
              
              // Try alternative selectors
              const alternativeSelectors = [
                'a[data-professionalid]',
                '.AppointmentChoicesList a',
                'a[href="javascript:;"]'
              ];
              
              for (let selector of alternativeSelectors) {
                const altElements = document.querySelectorAll(selector);
                console.log("=== DOCTOR SELECTION DEBUG: Found", altElements.length, "elements with selector:", selector);
                if (altElements.length > 0) {
                  console.log("=== DOCTOR SELECTION DEBUG: First alternative element:", altElements[0].outerHTML.substring(0, 200));
                }
              }
              
              return { needsDoctorSelection: false, reason: "No doctor selection elements found" };
            }
            
            // Log information about the doctor elements found
            for (let i = 0; i < Math.min(doctorElements.length, 3); i++) {
              console.log("=== DOCTOR SELECTION DEBUG: Doctor element", i, ":", doctorElements[i].outerHTML.substring(0, 200));
            }
            
            return {
              needsDoctorSelection: true,
              doctorCount: doctorElements.length,
              firstDoctorInfo: doctorElements[0].outerHTML.substring(0, 300)
            };
          }).then(function(result) {
            console.log("=== DOCTOR SELECTION DEBUG: Evaluation result:", result);
            
            if (!result.needsDoctorSelection) {
              console.log("=== DOCTOR SELECTION DEBUG: Doctor selection not needed -", result.reason);
              return false; // Doctor selection not needed
            }
            
            console.log("=== DOCTOR SELECTION DEBUG: Doctor selection needed! Found", result.doctorCount, "doctors");
            console.log("=== DOCTOR SELECTION DEBUG: Attempting to click first doctor ===");
            
            // Click on the first doctor
            return page.evaluate(function() {
              console.log("=== DOCTOR SELECTION DEBUG: Inside click evaluation ===");
              
              const doctorElements = document.querySelectorAll('.h-selectProfessional');
              if (doctorElements.length > 0) {
                console.log("=== DOCTOR SELECTION DEBUG: Clicking on first doctor element ===");
                try {
                  doctorElements[0].click();
                  console.log("=== DOCTOR SELECTION DEBUG: Click executed successfully ===");
                  return { success: true, doctorCount: doctorElements.length };
                } catch (error) {
                  console.log("=== DOCTOR SELECTION DEBUG: Click failed:", error.message);
                  return { success: false, error: error.message, doctorCount: doctorElements.length };
                }
              } else {
                console.log("=== DOCTOR SELECTION DEBUG: No doctor elements found during click attempt ===");
                return { success: false, error: "No doctor elements found", doctorCount: 0 };
              }
            }).then(function(doctorResult) {
              console.log("=== DOCTOR SELECTION DEBUG: Click result:", doctorResult);
              
              if (doctorResult.success) {
                console.log("=== DOCTOR SELECTION DEBUG: Successfully clicked on first doctor from", doctorResult.doctorCount, "available options");
                console.log("=== DOCTOR SELECTION DEBUG: Waiting for page to load after doctor selection ===");
                return waitForPageLoad(page).then(function() {
                  console.log("=== DOCTOR SELECTION DEBUG: Page loaded successfully after doctor selection ===");
                  return true; // Doctor selection was handled
                });
              } else {
                console.log("=== DOCTOR SELECTION DEBUG: Failed to find or click doctor selection - Error:", doctorResult.error);
                return false; // Doctor selection failed
              }
            });
          });
        }).catch(function(error) {
          console.log("=== DOCTOR SELECTION DEBUG: Error in handleDoctorSelection:", error.message);
          return false; // Error occurred, assume no doctor selection needed
        });
      }

      /**
       * Function to handle contact information form after time slot selection
       */
      function handleContactInformation(page) {
        console.log("Handling contact information form...");
        
        // Wait for the contact information page to load
        return waitForPageLoad(page).then(function() {
          // Check if we're on the contact information page
          return page.evaluate(function() {
            // Look for contact information form elements
            const emailField = document.querySelector('#ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_ClientEmail');
            const phoneField = document.querySelector('#ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_CellNumber');
            const languageFieldFrench = document.querySelector('#ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_LanguageF');
            const continueButton = document.querySelector('.buttonContinueCIF');
            
            return {
              hasContactForm: !!(emailField || phoneField || languageFieldFrench),
              hasEmailField: !!emailField,
              hasPhoneField: !!phoneField,
              hasLanguageField: !!languageFieldFrench,
              hasContinueButton: !!continueButton
            };
          });
        }).then(function(pageInfo) {
          if (!pageInfo.hasContactForm) {
            console.log("Contact information form not found, skipping...");
            return { success: true, message: "No contact form found" };
          }
          
          console.log("Contact information form detected, filling out fields...");
          
          // Fill email field
          return page.evaluate(function(email) {
            const emailField = document.querySelector('#ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_ClientEmail');
            if (emailField && email) {
              emailField.value = email;
              emailField.dispatchEvent(new Event('input', { bubbles: true }));
              emailField.dispatchEvent(new Event('change', { bubbles: true }));
              console.log("Email field filled with: " + email);
              return true;
            }
            return false;
          }, USER_CONFIG.email).then(function(emailFilled) {
            
            // Fill cellphone field
            return page.evaluate(function(cellphone) {
              const phoneField = document.querySelector('#ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_CellNumber');
              if (phoneField && cellphone) {
                phoneField.value = cellphone;
                phoneField.dispatchEvent(new Event('input', { bubbles: true }));
                phoneField.dispatchEvent(new Event('change', { bubbles: true }));
                console.log("Cellphone field filled with: " + cellphone);
                return true;
              }
              return false;
            }, USER_CONFIG.cellphone).then(function(phoneFilled) {
              
              // Set language preference
              return page.evaluate(function(language) {
                let languageSet = false;
                
                if (language && language.toLowerCase() === 'french') {
                  const frenchRadio = document.querySelector('#ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_LanguageF');
                  if (frenchRadio) {
                    frenchRadio.checked = true;
                    frenchRadio.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log("Language set to French");
                    languageSet = true;
                  }
                } else if (language && language.toLowerCase() === 'english') {
                  const englishRadio = document.querySelector('#ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_LanguageE');
                  if (englishRadio) {
                    englishRadio.checked = true;
                    englishRadio.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log("Language set to English");
                    languageSet = true;
                  }
                }
                
                return languageSet;
              }, USER_CONFIG.language).then(function(languageSet) {
                
                // Set communication method preferences (email)
                return page.evaluate(function() {
                  const emailCheckbox = document.querySelector('#ctl00_ContentPlaceHolderMP_ClientInformationForm1_ClientInfo_EmailChecked');
                  if (emailCheckbox) {
                    emailCheckbox.checked = true;
                    emailCheckbox.dispatchEvent(new Event('change', { bubbles: true }));
                    console.log("Email communication method selected");
                    return true;
                  }
                  return false;
                }).then(function(emailCommSet) {
                  
                  // Wait for form to settle before clicking Continue button
                  return new Promise(function(resolve) {
                    setTimeout(resolve, 2000);
                  }).then(function() {
                    
                    // Click the Continue button using the most reliable method
                    return page.evaluate(function() {
                      // Try method 1: Search by text content (most reliable)
                      const allButtons = document.querySelectorAll('button');
                      for (let i = 0; i < allButtons.length; i++) {
                        const btn = allButtons[i];
                        const text = (btn.textContent || btn.innerText || '').trim();
                        
                        if (text === 'Continuer' && !btn.disabled && btn.offsetParent !== null) {
                          btn.click();
                          console.log("Clicked Continue button using text search");
                          return true;
                        }
                      }
                      
                      // Fallback: try the class selector
                      const continueButton = document.querySelector('.buttonContinueCIF');
                      if (continueButton && !continueButton.disabled && continueButton.offsetParent !== null) {
                        continueButton.click();
                        console.log("Clicked Continue button using class selector");
                        return true;
                      }
                      
                      console.log("Could not find or click Continue button");
                      return false;
                    }).then(function(continueClicked) {
                      
                      if (continueClicked) {
                        // Wait for page to load after clicking continue
                        return waitForPageLoad(page).then(function() {
                          return {
                            success: true,
                            message: "Contact information filled and submitted successfully"
                          };
                        }).catch(function(loadError) {
                          return {
                            success: true, // Still consider it success since button was clicked
                            message: "Contact information submitted successfully"
                          };
                        });
                      } else {
                        return {
                          success: false,
                          message: "Could not find or click Continue button"
                        };
                      }
                    });
                  });
                });
              });
            });
          });
        }).catch(function(error) {
          console.log("Error handling contact information:", error);
          return {
            success: false,
            message: "Error handling contact information: " + error.message
          };
        });
      }

      /**
       * Function to check for error messages on the page
       * Returns true if error message is present, false otherwise
       */
      function checkForErrorMessage(page) {
        return page.evaluate(function() {
          const errorLabel = document.querySelector('label.control-label[for="ClientInformationForm_ErrorMessage"]');
          if (errorLabel) {
            const errorText = errorLabel.textContent || errorLabel.innerText || '';
            const hasError = errorText.trim().length > 0;
            if (hasError) {
              console.log("Error message detected: " + errorText.trim());
            }
            return hasError;
          }
          return false;
        });
      }

      /**
       * Function to check for acceptable time slots and book them with retry logic
       * Tries each time slot until one works or all fail, then returns to clinic search
       */
      function checkAndBookTimeSlotWithRetry(page) {
        console.log("Checking for acceptable time slots with retry logic...");

        // Wait for time slots to load
        return page.waitForFunction(function() {
          // Look for time slot buttons with class 'h-TimeButton timeButton btn btn-block'
          const timeSlots = document.querySelectorAll('.h-TimeButton.timeButton.btn.btn-block');
          // Or check if there's a message indicating no time slots
          const noSlots = document.querySelector('.noSlots, .noAppointments');
          return (timeSlots && timeSlots.length > 0) || noSlots;
        }, { timeout: 30000 }).catch(function(e) {
          console.log("Timeout waiting for time slots to load, continuing anyway...");
        }).then(function() {
          // Get our target date from user configuration
          const targetDate = USER_CONFIG.appointmentDate;

          // Calculate minimum acceptable time (current time + configured delay) - ONLY for today's appointments
          const currentTime = new Date();
          const delayHours = parseInt(USER_CONFIG.notBeforeThisDelay) || 2; // Default to 2 hours if invalid
          const minAcceptableTime = new Date(currentTime.getTime() + (delayHours * 60 * 60 * 1000));

          // Check if the target date is today
          const today = new Date();
          const targetDateParts = targetDate.split('-'); // DD-MM-YYYY
          const targetDateObj = new Date(
            parseInt(targetDateParts[2]), // year
            parseInt(targetDateParts[1]) - 1, // month (0-based)
            parseInt(targetDateParts[0]) // day
          );

          const isToday = (
            targetDateObj.getDate() === today.getDate() &&
            targetDateObj.getMonth() === today.getMonth() &&
            targetDateObj.getFullYear() === today.getFullYear()
          );

          // Parse time constraints from user configuration
          const afterThisTime = USER_CONFIG.afterThisTime;
          const beforeThisTime = USER_CONFIG.beforeThisTime;

          // Get all acceptable time slots
          return page.evaluate(function(targetDate, minAcceptableHour, minAcceptableMinute, isAppointmentToday, delayHours, afterThisTime, beforeThisTime) {
            // Find all time slot buttons
            const timeButtons = document.querySelectorAll('.h-TimeButton.timeButton.btn.btn-block');

            if (!timeButtons || timeButtons.length === 0) {
              return {
                status: 'none',
                message: 'No time slots available',
                slots: []
              };
            }

            // Find the displayed date - look at the column headers
            const dateHeaders = document.querySelectorAll('.header.date.weekday');
            const todayHeader = document.querySelector('.header.date.weekday.isToday');

            // First check if we have a today header (with our target date)
            let foundTargetDate = false;
            let displayedDate = '';

            if (todayHeader) {
              displayedDate = todayHeader.textContent.trim();
              // Check if this contains our target date day/month
              const targetDayMonth = targetDate.split('-').slice(0, 2);
              if (displayedDate.includes(targetDayMonth[0])) {
                foundTargetDate = true;
              }
            }

            // If not found in today header, check all date headers
            if (!foundTargetDate && dateHeaders && dateHeaders.length > 0) {
              for (let i = 0; i < dateHeaders.length; i++) {
                const text = dateHeaders[i].textContent.trim();
                // Check if this contains our target date
                const targetDay = parseInt(targetDate.split('-')[0]);
                // Look for the target day in the header text
                if (text.includes(targetDay)) {
                  displayedDate = text;
                  foundTargetDate = true;
                  break;
                }
              }
            }

            // If we still haven't found our target date in any headers
            if (!foundTargetDate) {
              // Look for our date in the title/heading of the page
              const title = document.querySelector('#availableAppointments .selectedDate');
              if (title) {
                displayedDate = title.textContent.trim();
                if (displayedDate.includes(targetDate.split('-')[0]) &&
                    displayedDate.includes(targetDate.split('-')[1])) {
                  foundTargetDate = true;
                }
              }

              // Also check the page content for French date format
              if (!foundTargetDate) {
                const pageContent = document.body.textContent || "";
                displayedDate = pageContent;

                // Convert target date to French format for comparison
                const targetDay = parseInt(targetDate.split('-')[0]); // 01 -> 1
                const targetMonth = parseInt(targetDate.split('-')[1]); // 04 -> 4
                const targetYear = targetDate.split('-')[2]; // 2025

                const frenchMonths = ['', 'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
                                    'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];

                const targetFrenchDate = targetDay + ' ' + frenchMonths[targetMonth] + ' ' + targetYear;

                // Check if the French date format appears in the page content
                if (pageContent.toLowerCase().includes(targetFrenchDate.toLowerCase())) {
                  foundTargetDate = true;
                  console.log("Found target date in French format: " + targetFrenchDate);
                }
              }
            }

            // If we couldn't find our target date anywhere, it's probably wrong date
            if (!foundTargetDate) {
              return {
                status: 'wrong_date',
                message: 'Target date ' + targetDate + ' not found in displayed dates: ' + displayedDate,
                slots: []
              };
            }

            // Parse time constraints
            let afterHour = null, afterMinute = null, beforeHour = null, beforeMinute = null;

            if (afterThisTime) {
              const afterParts = afterThisTime.split(':');
              afterHour = parseInt(afterParts[0]);
              afterMinute = parseInt(afterParts[1]);
            }

            if (beforeThisTime) {
              const beforeParts = beforeThisTime.split(':');
              beforeHour = parseInt(beforeParts[0]);
              beforeMinute = parseInt(beforeParts[1]);
            }

            // Find all acceptable time slots
            let acceptableSlots = [];
            let rejectedSlots = [];

            for (let i = 0; i < timeButtons.length; i++) {
              const button = timeButtons[i];
              const timeText = button.textContent.trim();

              // Extract hours and minutes (format like "11:20")
              const timeParts = timeText.match(/(\d{1,2}):(\d{2})/);
              if (!timeParts) continue;

              const hours = parseInt(timeParts[1]);
              const minutes = parseInt(timeParts[2]);

              // Create a Date object for this time
              const slotTime = new Date();
              slotTime.setHours(hours, minutes, 0, 0);

              // Check if time is acceptable
              let isTimeAcceptable = true;
              let rejectionReason = '';

              // Only apply delay restriction if appointment is for today
              if (isAppointmentToday) {
                // Check if time is at least the configured delay from now (only for today's appointments)
                if (!(slotTime.getHours() > minAcceptableHour ||
                    (slotTime.getHours() === minAcceptableHour && slotTime.getMinutes() >= minAcceptableMinute))) {
                  isTimeAcceptable = false;
                  rejectionReason = 'too early (within delay period)';
                }
              }

              // Check afterThisTime constraint
              if (isTimeAcceptable && afterHour !== null) {
                if (hours < afterHour || (hours === afterHour && minutes < afterMinute)) {
                  isTimeAcceptable = false;
                  rejectionReason = 'before afterThisTime (' + afterThisTime + ')';
                }
              }

              // Check beforeThisTime constraint
              if (isTimeAcceptable && beforeHour !== null) {
                if (hours > beforeHour || (hours === beforeHour && minutes >= beforeMinute)) {
                  isTimeAcceptable = false;
                  rejectionReason = 'after beforeThisTime (' + beforeThisTime + ')';
                }
              }

              if (isTimeAcceptable) {
                acceptableSlots.push({
                  button: button,
                  timeText: timeText,
                  hours: hours,
                  minutes: minutes,
                  slotTime: slotTime
                });
              } else {
                rejectedSlots.push({
                  time: timeText,
                  reason: rejectionReason
                });
              }
            }

            // Sort acceptable slots by time (earliest first)
            acceptableSlots.sort(function(a, b) {
              if (a.hours !== b.hours) {
                return a.hours - b.hours;
              }
              return a.minutes - b.minutes;
            });

            if (acceptableSlots.length > 0) {
              return {
                status: 'found',
                message: 'Found ' + acceptableSlots.length + ' acceptable time slot(s)',
                slots: acceptableSlots.map(function(slot) {
                  return {
                    timeText: slot.timeText,
                    hours: slot.hours,
                    minutes: slot.minutes
                  };
                })
              };
            } else {
              // Build detailed message about why slots were rejected
              let detailedMessage = 'No acceptable time slots found. ';

              if (rejectedSlots.length > 0) {
                detailedMessage += 'Rejected slots: ';
                const rejectionSummary = rejectedSlots.map(function(slot) {
                  return slot.time + ' (' + slot.reason + ')';
                }).join(', ');
                detailedMessage += rejectionSummary;
              }

              // Add constraint information
              const constraints = [];
              if (afterThisTime) constraints.push('after ' + afterThisTime);
              if (beforeThisTime) constraints.push('before ' + beforeThisTime);
              if (isAppointmentToday) constraints.push('at least ' + delayHours + ' hours from now');

              if (constraints.length > 0) {
                detailedMessage += '. Time constraints: ' + constraints.join(', ');
              }

              return {
                status: 'no_acceptable_slots',
                message: detailedMessage,
                slots: []
              };
            }
          }, targetDate, minAcceptableTime.getHours(), minAcceptableTime.getMinutes(), isToday, delayHours, afterThisTime, beforeThisTime);
        }).then(function(slotResult) {
          console.log(slotResult.message);

          // If no acceptable time slots were found, go back to search for another clinic
          if (slotResult.status !== 'found' || slotResult.slots.length === 0) {
            console.log("No acceptable time slots found. Going back to search for another clinic...");

            // Try to click the "retour" button with multiple selectors
            return page.evaluate(function() {
              // Try multiple ways to find and click the back button
              const selectors = [
                '#AppointmentBookingBackButton',
                '.h-BackButton',
                'a.h-BackButton.backButton.btn.withArrow',
                'a[id="AppointmentBookingBackButton"]',
                'a:contains("Retour")'
              ];

              for (let i = 0; i < selectors.length; i++) {
                const element = document.querySelector(selectors[i]);
                if (element && element.offsetParent !== null) { // Check if element is visible
                  console.log("Found back button with selector: " + selectors[i]);
                  element.click();
                  return { success: true, method: selectors[i] };
                }
              }

              // If no selector worked, try finding by text content
              const allLinks = document.querySelectorAll('a, button');
              for (let i = 0; i < allLinks.length; i++) {
                if (allLinks[i].textContent.trim().toLowerCase().includes('retour')) {
                  console.log("Found back button by text content: " + allLinks[i].textContent.trim());
                  allLinks[i].click();
                  return { success: true, method: 'text-content' };
                }
              }

              return { success: false, method: 'none' };
            }).then(function(clickResult) {
              if (clickResult.success) {
                console.log("Clicked on back button using method: " + clickResult.method);
                return waitForPageLoad(page);
              } else {
                throw new Error("Could not find back button with any method");
              }
            }).then(function() {
              return {
                success: false,
                message: slotResult.message
              };
            }).catch(function(e) {
              console.log("Error clicking back button:", e);

              // Try using browser navigation as a fallback
              return page.goBack()
                .then(function() {
                  console.log("Used browser back navigation");
                  return waitForPageLoad(page);
                })
                .then(function() {
                  return {
                    success: false,
                    message: "Used back navigation: " + slotResult.message
                  };
                })
                .catch(function(backError) {
                  console.log("Browser back navigation also failed:", backError);
                  return {
                    success: false,
                    message: "Error navigating back: " + e.message
                  };
                });
            });
          }

          // Try each acceptable time slot until one works or all fail
          let currentSlotIndex = 0;

          function tryNextTimeSlot() {
            if (currentSlotIndex >= slotResult.slots.length) {
              // All time slots have been tried and failed, go back to clinic search
              console.log("All time slots have been tried and failed. Going back to search for another clinic...");

              // Try to click the "retour" button with multiple selectors
              return page.evaluate(function() {
                // Try multiple ways to find and click the back button
                const selectors = [
                  '#AppointmentBookingBackButton',
                  '.h-BackButton',
                  'a.h-BackButton.backButton.btn.withArrow',
                  'a[id="AppointmentBookingBackButton"]',
                  'a:contains("Retour")'
                ];

                for (let i = 0; i < selectors.length; i++) {
                  const element = document.querySelector(selectors[i]);
                  if (element && element.offsetParent !== null) { // Check if element is visible
                    console.log("Found back button with selector: " + selectors[i]);
                    element.click();
                    return { success: true, method: selectors[i] };
                  }
                }

                // If no selector worked, try finding by text content
                const allLinks = document.querySelectorAll('a, button');
                for (let i = 0; i < allLinks.length; i++) {
                  if (allLinks[i].textContent.trim().toLowerCase().includes('retour')) {
                    console.log("Found back button by text content: " + allLinks[i].textContent.trim());
                    allLinks[i].click();
                    return { success: true, method: 'text-content' };
                  }
                }

                return { success: false, method: 'none' };
              }).then(function(clickResult) {
                if (clickResult.success) {
                  console.log("Clicked on back button using method: " + clickResult.method);
                  return waitForPageLoad(page);
                } else {
                  throw new Error("Could not find back button with any method");
                }
              }).then(function() {
                return {
                  success: false,
                  message: "All time slots failed, returned to clinic search"
                };
              }).catch(function(e) {
                console.log("Error clicking back button:", e);

                // Try using browser navigation as a fallback
                return page.goBack()
                  .then(function() {
                    console.log("Used browser back navigation");
                    return waitForPageLoad(page);
                  })
                  .then(function() {
                    return {
                      success: false,
                      message: "All time slots failed, used back navigation to clinic search"
                    };
                  })
                  .catch(function(backError) {
                    console.log("Browser back navigation also failed:", backError);
                    return {
                      success: false,
                      message: "Error navigating back after all time slots failed: " + e.message
                    };
                  });
              });
            }

            const currentSlot = slotResult.slots[currentSlotIndex];
            console.log('Trying time slot ' + (currentSlotIndex + 1) + ' of ' + slotResult.slots.length + ': ' + currentSlot.timeText + '...');

            // Click the current time slot
            return page.evaluate(function(timeText) {
              const buttons = document.querySelectorAll('.h-TimeButton.timeButton.btn.btn-block');

              for (let i = 0; i < buttons.length; i++) {
                if (buttons[i].textContent.trim() === timeText) {
                  buttons[i].click();
                  return true;
                }
              }
              return false;
            }, currentSlot.timeText)
            .then(function(clicked) {
              if (!clicked) {
                console.log('Failed to click time slot ' + currentSlot.timeText);
                currentSlotIndex++;
                return tryNextTimeSlot();
              }

              console.log('Clicked on time slot ' + currentSlot.timeText);
              return waitForPageLoad(page);
            })
            .then(function() {
              // Check for error message
              return checkForErrorMessage(page);
            })
            .then(function(hasError) {
              if (hasError) {
                console.log('Error message detected for time slot ' + currentSlot.timeText + '. Trying next slot...');
                currentSlotIndex++;
                return tryNextTimeSlot();
              } else {
                console.log('No error message detected. Time slot ' + currentSlot.timeText + ' appears to be successful.');

                // Check for and handle doctor selection page
                return handleDoctorSelection(page).then(function(doctorSelectionHandled) {
                  if (doctorSelectionHandled) {
                    console.log('Doctor selection completed successfully');
                  } else {
                    console.log('No doctor selection needed, continuing with booking');
                  }

                  // After successful time slot booking, handle contact information
                  return handleContactInformation(page).then(function(contactResult) {
                    if (contactResult.success) {
                      console.log('Contact information filled successfully');
                      return {
                        success: true,
                        message: 'Selected appointment at ' + currentSlot.timeText + (doctorSelectionHandled ? ' (with doctor selection)' : '') + ' and filled contact info'
                      };
                    } else {
                      console.log('Warning: Contact information filling failed: ' + contactResult.message);
                      return {
                        success: true,
                        message: 'Selected appointment at ' + currentSlot.timeText + (doctorSelectionHandled ? ' (with doctor selection)' : '') + ' but contact info failed: ' + contactResult.message
                      };
                    }
                  });
                });
              }
            })
            .catch(function(e) {
              console.log("Error trying time slot " + currentSlot.timeText + ":", e);
              currentSlotIndex++;
              return tryNextTimeSlot();
            });
          }

          // Start trying time slots
          return tryNextTimeSlot();
        });
      }

      /**
       * Function to check for acceptable time slots and book the earliest one,
       * or go back to continue searching if no acceptable slots found
       */
      function checkAndBookTimeSlot(page) {
        console.log("Checking for acceptable time slots...");
        
        // Wait for time slots to load
        return page.waitForFunction(function() {
          // Look for time slot buttons with class 'h-TimeButton timeButton btn btn-block'
          const timeSlots = document.querySelectorAll('.h-TimeButton.timeButton.btn.btn-block');
          // Or check if there's a message indicating no time slots
          const noSlots = document.querySelector('.noSlots, .noAppointments');
          return (timeSlots && timeSlots.length > 0) || noSlots;
        }, { timeout: 30000 }).catch(function(e) {
          console.log("Timeout waiting for time slots to load, continuing anyway...");
        }).then(function() {
          // Get our target date from user configuration
          const targetDate = USER_CONFIG.appointmentDate;
          
          // Calculate minimum acceptable time (current time + configured delay) - ONLY for today's appointments
          const currentTime = new Date();
          const delayHours = parseInt(USER_CONFIG.notBeforeThisDelay) || 2; // Default to 2 hours if invalid
          const minAcceptableTime = new Date(currentTime.getTime() + (delayHours * 60 * 60 * 1000));
          
          // Check if the target date is today
          const today = new Date();
          const targetDateParts = targetDate.split('-'); // DD-MM-YYYY
          const targetDateObj = new Date(
            parseInt(targetDateParts[2]), // year
            parseInt(targetDateParts[1]) - 1, // month (0-based)
            parseInt(targetDateParts[0]) // day
          );
          
          const isToday = (
            targetDateObj.getDate() === today.getDate() &&
            targetDateObj.getMonth() === today.getMonth() &&
            targetDateObj.getFullYear() === today.getFullYear()
          );
          
          // Parse time constraints from user configuration
          const afterThisTime = USER_CONFIG.afterThisTime;
          const beforeThisTime = USER_CONFIG.beforeThisTime;
          
          // Check if there are any acceptable time slots
          return page.evaluate(function(targetDate, minAcceptableHour, minAcceptableMinute, isAppointmentToday, delayHours, afterThisTime, beforeThisTime) {
            // Find all time slot buttons
            const timeButtons = document.querySelectorAll('.h-TimeButton.timeButton.btn.btn-block');
            
            if (!timeButtons || timeButtons.length === 0) {
              return {
                status: 'none',
                message: 'No time slots available'
              };
            }
            
            // Find the displayed date - look at the column headers
            const dateHeaders = document.querySelectorAll('.header.date.weekday');
            const todayHeader = document.querySelector('.header.date.weekday.isToday');
            
            // First check if we have a today header (with our target date)
            let foundTargetDate = false;
            let displayedDate = '';
            
            if (todayHeader) {
              displayedDate = todayHeader.textContent.trim();
              // Check if this contains our target date day/month
              const targetDayMonth = targetDate.split('-').slice(0, 2);
              if (displayedDate.includes(targetDayMonth[0])) {
                foundTargetDate = true;
              }
            }
            
            // If not found in today header, check all date headers
            if (!foundTargetDate && dateHeaders && dateHeaders.length > 0) {
              for (let i = 0; i < dateHeaders.length; i++) {
                const text = dateHeaders[i].textContent.trim();
                // Check if this contains our target date
                const targetDay = parseInt(targetDate.split('-')[0]);
                // Look for the target day in the header text
                if (text.includes(targetDay)) {
                  displayedDate = text;
                  foundTargetDate = true;
                  break;
                }
              }
            }
            
            // If we still haven't found our target date in any headers
            if (!foundTargetDate) {
              // Look for our date in the title/heading of the page
              const title = document.querySelector('#availableAppointments .selectedDate');
              if (title) {
                displayedDate = title.textContent.trim();
                if (displayedDate.includes(targetDate.split('-')[0]) && 
                    displayedDate.includes(targetDate.split('-')[1])) {
                  foundTargetDate = true;
                }
              }
              
              // Also check the page content for French date format
              if (!foundTargetDate) {
                const pageContent = document.body.textContent || "";
                displayedDate = pageContent;
                
                // Convert target date to French format for comparison
                const targetDay = parseInt(targetDate.split('-')[0]); // 01 -> 1
                const targetMonth = parseInt(targetDate.split('-')[1]); // 04 -> 4
                const targetYear = targetDate.split('-')[2]; // 2025
                
                const frenchMonths = ['', 'janvier', 'février', 'mars', 'avril', 'mai', 'juin', 
                                    'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'];
                
                const targetFrenchDate = targetDay + ' ' + frenchMonths[targetMonth] + ' ' + targetYear;
                
                // Check if the French date format appears in the page content
                if (pageContent.toLowerCase().includes(targetFrenchDate.toLowerCase())) {
                  foundTargetDate = true;
                  console.log("Found target date in French format: " + targetFrenchDate);
                }
              }
            }
            
            // If we couldn't find our target date anywhere, it's probably wrong date
            if (!foundTargetDate) {
              return {
                status: 'wrong_date',
                message: 'Target date ' + targetDate + ' not found in displayed dates: ' + displayedDate
              };
            }
            
            // Parse time constraints
            let afterHour = null, afterMinute = null, beforeHour = null, beforeMinute = null;
            
            if (afterThisTime) {
              const afterParts = afterThisTime.split(':');
              afterHour = parseInt(afterParts[0]);
              afterMinute = parseInt(afterParts[1]);
            }
            
            if (beforeThisTime) {
              const beforeParts = beforeThisTime.split(':');
              beforeHour = parseInt(beforeParts[0]);
              beforeMinute = parseInt(beforeParts[1]);
            }
            
            // Find the earliest acceptable time slot
            // TODO: FUTURE FEATURE - Filter by momentOfDay preference (morning/afternoon/evening/asap)
            let earliestSlot = null;
            let earliestTime = null;
            let rejectedSlots = [];
            
            for (let i = 0; i < timeButtons.length; i++) {
              const button = timeButtons[i];
              const timeText = button.textContent.trim();
              
              // Extract hours and minutes (format like "11:20")
              const timeParts = timeText.match(/(\d{1,2}):(\d{2})/);
              if (!timeParts) continue;
              
              const hours = parseInt(timeParts[1]);
              const minutes = parseInt(timeParts[2]);
              
              // Create a Date object for this time
              const slotTime = new Date();
              slotTime.setHours(hours, minutes, 0, 0);
              
              // Check if time is acceptable
              let isTimeAcceptable = true;
              let rejectionReason = '';
              
              // Only apply delay restriction if appointment is for today
              if (isAppointmentToday) {
                // Check if time is at least the configured delay from now (only for today's appointments)
                if (!(slotTime.getHours() > minAcceptableHour || 
                    (slotTime.getHours() === minAcceptableHour && slotTime.getMinutes() >= minAcceptableMinute))) {
                  isTimeAcceptable = false;
                  rejectionReason = 'too early (within delay period)';
                }
              }
              
              // Check afterThisTime constraint
              if (isTimeAcceptable && afterHour !== null) {
                if (hours < afterHour || (hours === afterHour && minutes < afterMinute)) {
                  isTimeAcceptable = false;
                  rejectionReason = 'before afterThisTime (' + afterThisTime + ')';
                }
              }
              
              // Check beforeThisTime constraint
              if (isTimeAcceptable && beforeHour !== null) {
                if (hours > beforeHour || (hours === beforeHour && minutes >= beforeMinute)) {
                  isTimeAcceptable = false;
                  rejectionReason = 'after beforeThisTime (' + beforeThisTime + ')';
                }
              }
              
              if (isTimeAcceptable) {
                // If this is the first acceptable slot or earlier than previous best
                if (!earliestSlot || 
                    (slotTime.getHours() < earliestTime.getHours()) || 
                    (slotTime.getHours() === earliestTime.getHours() && slotTime.getMinutes() < earliestTime.getMinutes())) {
                  earliestSlot = button;
                  earliestTime = slotTime;
                }
              } else {
                rejectedSlots.push({
                  time: timeText,
                  reason: rejectionReason
                });
              }
            }
            
            if (earliestSlot) {
              return {
                status: 'found',
                message: 'Found acceptable time slot: ' + earliestTime.getHours() + ':' + 
                         earliestTime.getMinutes().toString().padStart(2, '0'),
                slotText: earliestSlot.textContent.trim(),
                slotId: earliestSlot.getAttribute('data-ids')
              };
            } else {
              // Build detailed message about why slots were rejected
              let detailedMessage = 'No acceptable time slots found. ';
              
              if (rejectedSlots.length > 0) {
                detailedMessage += 'Rejected slots: ';
                const rejectionSummary = rejectedSlots.map(function(slot) {
                  return slot.time + ' (' + slot.reason + ')';
                }).join(', ');
                detailedMessage += rejectionSummary;
              }
              
              // Add constraint information
              const constraints = [];
              if (afterThisTime) constraints.push('after ' + afterThisTime);
              if (beforeThisTime) constraints.push('before ' + beforeThisTime);
              if (isAppointmentToday) constraints.push('at least ' + delayHours + ' hours from now');
              
              if (constraints.length > 0) {
                detailedMessage += '. Time constraints: ' + constraints.join(', ');
              }
              
              return {
                status: 'no_acceptable_slots',
                message: detailedMessage
              };
            }
                      }, targetDate, minAcceptableTime.getHours(), minAcceptableTime.getMinutes(), isToday, delayHours, afterThisTime, beforeThisTime);
        }).then(function(slotResult) {
          console.log(slotResult.message);
          
          // If an acceptable time slot was found, click it
          if (slotResult.status === 'found') {
            console.log('Clicking on the earliest acceptable time slot: ' + slotResult.slotText + '...');
            
            // Try to click the button by its text content
            return page.evaluate(function(timeText) {
              const buttons = document.querySelectorAll('.h-TimeButton.timeButton.btn.btn-block');
              
              for (let i = 0; i < buttons.length; i++) {
                if (buttons[i].textContent.trim() === timeText) {
                  buttons[i].click();
                  return true;
                }
              }
              return false;
            }, slotResult.slotText)
            .then(function() {
              console.log('Clicked on time slot ' + slotResult.slotText);
              return waitForPageLoad(page);
            })
            .then(function() {
              // NEW: Check for and handle doctor selection page
              return handleDoctorSelection(page);
            })
            .then(function(doctorSelectionHandled) {
              if (doctorSelectionHandled) {
                console.log('Doctor selection completed successfully');
              } else {
                console.log('No doctor selection needed, continuing with booking');
              }
              
              // After successful time slot booking, handle contact information
              return handleContactInformation(page).then(function(contactResult) {
                if (contactResult.success) {
                  console.log('Contact information filled successfully');
                  return {
                    success: true,
                    message: 'Selected appointment at ' + slotResult.slotText + (doctorSelectionHandled ? ' (with doctor selection)' : '') + ' and filled contact info'
                  };
                } else {
                  console.log('Warning: Contact information filling failed: ' + contactResult.message);
                  return {
                    success: true,
                    message: 'Selected appointment at ' + slotResult.slotText + (doctorSelectionHandled ? ' (with doctor selection)' : '') + ' but contact info failed: ' + contactResult.message
                  };
                }
              });
            })
            .catch(function(e) {
              console.log("Error in time slot booking process:", e);
              return {
                success: false,
                message: "Error selecting time slot or doctor: " + e.message
              };
            });
          } else {
            // No acceptable time slots, go back to search for another clinic
            console.log("No acceptable time slots found. Going back to search for another clinic...");
            
            // Try to click the "retour" button with multiple selectors
            return page.evaluate(function() {
              // Try multiple ways to find and click the back button
              const selectors = [
                '#AppointmentBookingBackButton',
                '.h-BackButton',
                'a.h-BackButton.backButton.btn.withArrow',
                'a[id="AppointmentBookingBackButton"]',
                'a:contains("Retour")'
              ];
              
              for (let i = 0; i < selectors.length; i++) {
                const element = document.querySelector(selectors[i]);
                if (element && element.offsetParent !== null) { // Check if element is visible
                  console.log("Found back button with selector: " + selectors[i]);
                  element.click();
                  return { success: true, method: selectors[i] };
                }
              }
              
              // If no selector worked, try finding by text content
              const allLinks = document.querySelectorAll('a, button');
              for (let i = 0; i < allLinks.length; i++) {
                if (allLinks[i].textContent.trim().toLowerCase().includes('retour')) {
                  console.log("Found back button by text content: " + allLinks[i].textContent.trim());
                  allLinks[i].click();
                  return { success: true, method: 'text-content' };
                }
              }
              
              return { success: false, method: 'none' };
            }).then(function(clickResult) {
              if (clickResult.success) {
                console.log("Clicked on back button using method: " + clickResult.method);
                return waitForPageLoad(page);
              } else {
                throw new Error("Could not find back button with any method");
              }
            }).then(function() {
              return {
                success: false,
                message: slotResult.message
              };
            }).catch(function(e) {
              console.log("Error clicking back button:", e);
              
              // Try using browser navigation as a fallback
              return page.goBack()
                .then(function() {
                  console.log("Used browser back navigation");
                  return waitForPageLoad(page);
                })
                .then(function() {
                  return {
                    success: false,
                    message: "Used back navigation: " + slotResult.message
                  };
                })
                .catch(function(backError) {
                  console.log("Browser back navigation also failed:", backError);
                  return {
                    success: false,
                    message: "Error navigating back: " + e.message
                  };
                });
            });
          }
        });
      }

      // Function to find closest clinic and book time slot
      function findClosestClinic(page) {
        console.log("Starting search for the closest clinic...");
        
        let attempt = 1;
        
        // Function to be called recursively to implement the loop
        function searchLoop() {
          // Wait for either clinic results or the "no disponibilities" message
          return page.waitForFunction(function() {
            // Check if the "no disponibilities" container is visible
            let noDisponibilitiesEl = document.querySelector('#clinicsWithNoDisponibilitiesContainer');
            if (noDisponibilitiesEl && noDisponibilitiesEl.style.display !== 'none') return true;
            
            // Check if clinic results are available
            if (document.querySelector('.h-selectClinic')) return true;
            
            // Neither condition met yet, keep waiting
            return false;
          }, { timeout: 30000 }).catch(function(e) {
            console.log("Timeout waiting for results, continuing anyway...");
          }).then(function() {
            // Check if the "no disponibilities" message is visible
            return page.evaluate(function() {
              const container = document.querySelector('#clinicsWithNoDisponibilitiesContainer');
              return container && container.style.display !== 'none';
            });
          }).then(function(noDisponibilities) {
            // If no clinics available, click search button and continue loop
            if (noDisponibilities) {
              console.log("No clinics available. Clicking search button...");
              
              // Click the search button
              return page.click('.h-SearchButton, button.btn.btn-primary').catch(function(e) {
                // Fallback to XPath if needed
                return page.click('xpath///*[@id="aspnetForm"]/div[3]/div/div/div[1]/div[7]/div[5]/div/button');
              }).then(function() {
                // Increment attempt and continue loop
                attempt++;
                console.log("I'm here");
                return searchLoop(); // Recursive call to continue loop
              });
            }
            
            // If we get here, clinics are available!
            console.log("Clinics available! Finding the closest one...");
            
            // Find all clinics and their distances
            return page.evaluate(function(visitedClinicNames) {
              // Get all clinic elements - make sure we get everything that could be a clinic
              const clinicElements = document.querySelectorAll('.h-selectClinic');
              console.log("Total clinics found: " + clinicElements.length);
              
              // Process each clinic to extract distance
              const clinics = Array.from(clinicElements).map(function(clinic, index) {
                // Get the distance (looking for patterns like "19,97 km")
                const text = clinic.textContent || "";
                
                // Debug info to console
                console.log("Clinic " + (index + 1) + " text: " + text.substring(0, 100) + "...");
                
                // Get the clinic name
                let name = "Clinic " + (index + 1);
                const nameElement = clinic.querySelector('h2, h3, .clinic-title');
                if (nameElement) {
                  name = nameElement.textContent.trim();
                }
                
                // Try different patterns to match distance
                const distanceMatch = text.match(/(\d+[,.]\d+)\s*km/) || text.match(/(\d+)[,.]\s*(\d+)\s*km/);
                
                let distance = 9999;
                if (distanceMatch) {
                  // If we have a match with groups, parse the distance
                  if (distanceMatch[2]) {
                    // Format like "19,97" - combine groups and parse
                    distance = parseFloat(distanceMatch[1] + "." + distanceMatch[2]);
                  } else {
                    // Format like "19,97 km" - just replace comma and parse
                    distance = parseFloat(distanceMatch[1].replace(',', '.'));
                  }
                  console.log("Found distance: " + distance + " km");
                } else {
                  console.log("No distance found in this clinic");
                }
                
                // Check if this clinic has been visited before
                const isVisited = visitedClinicNames.includes(name);
                
                return {
                  index: index,
                  name: name,
                  distance: distance,
                  selector: ".h-selectClinic:nth-child(" + (index + 1) + ")",
                  rawText: text.substring(0, 200), // Save some text for debugging
                  visited: isVisited
                };
              });
              
              return clinics;
            }, Array.from(visitedClinics)).then(function(clinicInfo) {
              // Safety check to ensure clinicInfo is defined and is an array
              if (!clinicInfo || !Array.isArray(clinicInfo)) {
                console.log("Error: clinicInfo is not defined or not an array. Retrying...");
                attempt++;
                return searchLoop(); // Recursive call to continue loop
              }
              
              console.log("Found " + clinicInfo.length + " clinics");
              
              // If no clinics were found (shouldn't happen, but just in case)
              if (clinicInfo.length === 0) {
                console.log("No clinics found in the results. Retrying...");
                attempt++;
                return searchLoop(); // Recursive call to continue loop
              }
              
              // Filter out clinics without distance info
              let clinicsWithDistance = clinicInfo.filter(function(clinic) {
                return clinic.distance !== 9999;
              });
              
              // Safety check for clinicsWithDistance
              if (!clinicsWithDistance || !Array.isArray(clinicsWithDistance)) {
                console.log("Error: clinicsWithDistance is not defined or not an array. Using original clinicInfo...");
                clinicsWithDistance = clinicInfo;
              }
              
              console.log("Found " + clinicsWithDistance.length + " clinics with distance info");
              
              if (clinicsWithDistance.length === 0) {
                console.log("No clinics with distance info found. Using all clinics.");
                // If no clinics have distance info, use all of them
                clinicsWithDistance = clinicInfo;
              }
              
              // Filter out already visited clinics
              const unvisitedClinics = clinicsWithDistance.filter(function(clinic) {
                return !clinic.visited;
              });
              
              // Filter clinics by transportation distance preference
              const maxDistance = parseFloat(USER_CONFIG.transportationDistance);
              const clinicsWithinDistance = unvisitedClinics.filter(function(clinic) {
                return clinic.distance <= maxDistance;
              });
              
              console.log(`Found ${clinicsWithinDistance.length} unvisited clinics within ${maxDistance} km`);
              
              // If no clinics are within the preferred distance, log and continue searching
              if (clinicsWithinDistance.length === 0) {
                console.log(`No clinics found within ${maxDistance} km. Available clinics:`);
                unvisitedClinics.forEach(function(clinic, i) {
                  console.log(`  ${i + 1}. ${clinic.name} - ${clinic.distance} km`);
                });
                console.log("Continuing search for more clinics...");
                
                // Click the search button to find more clinics
                return page.click('.h-SearchButton, button.btn.btn-primary').catch(function(e) {
                  // Fallback to XPath if needed
                  return page.click('xpath///*[@id="aspnetForm"]/div[3]/div/div/div[1]/div[7]/div[5]/div/button');
                }).then(function() {
                  attempt++;
                  return searchLoop(); // Recursive call to continue loop
                });
              }
              
              // Safety check for clinicsWithinDistance
              if (!clinicsWithinDistance || !Array.isArray(clinicsWithinDistance)) {
                console.log("Error: clinicsWithinDistance is not defined or not an array. Retrying...");
                attempt++;
                return searchLoop(); // Recursive call to continue loop
              }
              
              // Sort clinics by distance
              clinicsWithinDistance.sort(function(a, b) {
                return a.distance - b.distance;
              });
              
              // Log all clinics within distance for debugging
              console.log(`All clinics within ${maxDistance} km by distance:`);
              clinicsWithinDistance.forEach(function(clinic, i) {
                console.log(i + 1 + ". " + clinic.name + " - " + clinic.distance + " km");
              });
              
              // Get the closest clinic within the transportation distance
              const closest = clinicsWithinDistance[0];
              console.log("Closest unvisited clinic: " + closest.name + " at " + closest.distance + " km");
              
              // Add this clinic to our visited set
              visitedClinics.add(closest.name);
              console.log("Added " + closest.name + " to visited clinics. Total visited: " + visitedClinics.size);
              
              // Click on the closest clinic
              return page.evaluate(function(closestInfo) {
                console.log("Attempting to click on clinic: " + closestInfo.name);
                
                // Try to find the clinic by name and distance
                const clinics = document.querySelectorAll('.h-selectClinic');
                
                for (let i = 0; i < clinics.length; i++) {
                  const clinic = clinics[i];
                  const clinicText = clinic.textContent || "";
                  
                  // Check if this clinic has both the name and distance
                  if (clinicText.includes(closestInfo.name) && 
                      clinicText.includes(closestInfo.distanceText)) {
                    console.log("Found exact match for clinic by name and distance");
                    clinic.click();
                    return { clicked: true, method: "exact match" };
                  }
                }
                
                // If exact match failed, try just by closest.index which is the DOM position
                if (clinics[closestInfo.index]) {
                  console.log("Clicking by index: " + closestInfo.index);
                  clinics[closestInfo.index].click();
                  return { clicked: true, method: "index" };
                }
                
                // Last resort: try to find any clinic with similar distance
                for (let i = 0; i < clinics.length; i++) {
                  if (clinics[i].textContent.includes(closestInfo.distanceText)) {
                    console.log("Found clinic with matching distance: " + closestInfo.distanceText);
                    clinics[i].click();
                    return { clicked: true, method: "distance only" };
                  }
                }
                
                return { clicked: false };
              }, {
                name: closest.name,
                index: closest.index,
                distanceText: closest.distance.toString().replace('.', ',') + " km"
              }).then(function() {
                console.log("Clicked on closest clinic");
                return waitForPageLoad(page); // Use waitForPageLoad instead of timeout
              }).then(function() {
                // Add 20 second delay after clinic selection (temporary for testing)
                console.log("Adding 20 second delay after clinic selection...");
                return new Promise(function(resolve) {
                  setTimeout(resolve, 20000);
                });
              }).then(function() {
                // Check for available time slots
                console.log("Checking for available time slots...");
                return checkAndBookTimeSlotWithRetry(page);
              }).then(function(timeSlotResult) {
                // If we successfully found and booked a time slot, we're done
                if (timeSlotResult.success) {
                  return {
                    success: true,
                    clinicName: closest.name,
                    distance: closest.distance,
                    attempts: attempt,
                    appointment: timeSlotResult.message
                  };
                }
                
                // If not, we continue the loop to find another clinic
                console.log("No suitable time slots at this clinic. Continuing search...");
                attempt++;
                return searchLoop(); // Recursive call to continue loop
              }).catch(function(e) {
                console.log("Error clicking on closest clinic:", e);
                attempt++;
                return searchLoop(); // Recursive call to continue loop
              });
            });
          });
        }
        
        // Start the recursive loop
        return searchLoop();
      }

      // Navigate to the Quebec health appointment website
      return page.goto('https://rvsq.gouv.qc.ca/prendrerendezvous/Principale.aspx')
        .then(function() {
          // Accept cookies if the banner appears
          return page.waitForSelector('#btnToutAccepter', { timeout: 5000 }).then(function() {
            return page.click('#btnToutAccepter');
          }).catch(function(e) {
            console.log('Cookie banner not found or already accepted');
          });
        }).then(function() {
          // Fill out the identification form
          return page.type('#ctl00_ContentPlaceHolderMP_AssureForm_FirstName', USER_CONFIG.firstName);
        }).then(function() {
          return page.type('#ctl00_ContentPlaceHolderMP_AssureForm_LastName', USER_CONFIG.lastName);
        }).then(function() {
          return page.type('#ctl00_ContentPlaceHolderMP_AssureForm_NAM', USER_CONFIG.healthCardNumber);
        }).then(function() {
          return page.type('#ctl00_ContentPlaceHolderMP_AssureForm_CardSeqNumber', USER_CONFIG.cardSequenceNumber);
        }).then(function() {
          return page.type('#ctl00_ContentPlaceHolderMP_AssureForm_Day', USER_CONFIG.birthDay);
        }).then(function() {
          return page.select('#ctl00_ContentPlaceHolderMP_AssureForm_Month', USER_CONFIG.birthMonth);
        }).then(function() {
          return page.type('#ctl00_ContentPlaceHolderMP_AssureForm_Year', USER_CONFIG.birthYear);
        }).then(function() {
          return page.click('#AssureForm_CSTMT');
        }).then(function() {
          return page.click('#ctl00_ContentPlaceHolderMP_myButton');
        }).then(function() {
          return page.waitForNavigation({ waitUntil: 'networkidle0' });
        }).then(function() {
          // Wait for page to load after login
          return waitForPageLoad(page);
        }).then(function() {
          // Check for appointment options and handle accordingly
          return page.evaluate(function() {
            const elements = document.querySelectorAll('*');
            
            // First check: "Prendre rendez-vous avec mon professionnel"
            for (let i = 0; i < elements.length; i++) {
              const el = elements[i];
              if (el.textContent && el.textContent.includes('Prendre rendez-vous avec mon professionnel')) {
                let current = el;
                while (current && !current.tagName.match(/^(A|BUTTON)$/i) && !current.onclick) {
                  current = current.parentElement;
                }
                
                if (current) {
                  current.click();
                  return {
                    text: "Clicked on professional appointment option",
                    option: 1
                  };
                }
              }
            }
            
            // Second check: "Prendre rendez-vous dans une clinique à proximité"
            for (let i = 0; i < elements.length; i++) {
              const el = elements[i];
              if (el.textContent && el.textContent.includes('Prendre rendez-vous dans une clinique à proximité')) {
                let current = el;
                while (current && !current.tagName.match(/^(A|BUTTON)$/i) && !current.onclick) {
                  current = current.parentElement;
                }
                
                if (current) {
                  current.click();
                  return {
                    text: "Clicked on find appointment option",
                    option: 2
                  };
                }
              }
            }
            
            return {
              text: "Could not find any appointment option",
              option: 0
            };
          });
        }).then(function(appointmentResult) {
          console.log(appointmentResult.text);
          
          // Wait for page to load after clicking
          return waitForPageLoad(page).then(function() {
            return appointmentResult;
          });
        }).then(function(appointmentResult) {
          // Option 1 flow
          if (appointmentResult.option === 1) {
            // Select reason for consultation
            return page.select('#consultingReason', USER_CONFIG.consultationReason)
              .then(function() {
                return page.click('button.h-SearchButton.btn.btn-primary');
              }).then(function() {
                return waitForPageLoad(page);
              }).then(function() {
                // Click on the GMF button/box
                return page.evaluate(function() {
                  const containers = document.querySelectorAll('.thumbnail');
                  for (let i = 0; i < containers.length; i++) {
                    if (containers[i].textContent.includes('GMF')) {
                      console.log("Found container with GMF text");
                      containers[i].click();
                      return "Clicked on GMF option";
                    }
                  }
                  return "Could not find GMF option";
                });
              }).then(function(gmfResult) {
                console.log(gmfResult);
                return waitForPageLoad(page);
              }).then(function() {
                // Click the search button
                return page.evaluate(function() {
                  const buttons = document.querySelectorAll('button');
                  for (let i = 0; i < buttons.length; i++) {
                    if (buttons[i].textContent.trim() === 'Rechercher') {
                      buttons[i].click();
                      return "Clicked on Rechercher button";
                    }
                  }
                  return "Could not find Rechercher button";
                });
              }).then(function(searchResult) {
                console.log(searchResult);
                return waitForPageLoad(page);
              }).then(function() {
                // Click on the "Prendre rendez-vous dans une clinique à proximité" option
                return page.evaluate(function() {
                  const containers = document.querySelectorAll('.thumbnail');
                  for (let i = 0; i < containers.length; i++) {
                    if (containers[i].textContent.includes('Prendre rendez-vous dans une clinique à proximité')) {
                      console.log("Found container with 'clinique à proximité' text");
                      containers[i].click();
                      return "Clicked on clinique à proximité option";
                    }
                  }
                  return "Could not find clinique à proximité option";
                });
              }).then(function(proximiteResult) {
                console.log(proximiteResult);
                return waitForPageLoad(page);
              });
          } else {
            // Option 2 flow - no additional steps needed
            return Promise.resolve();
          }
        }).then(function() {
          // Both paths continue here with date setting
          
          // Set the date from user configuration
          return page.evaluate(function(appointmentDate) {
            const dateInput = document.querySelector('input[type="text"][placeholder="jj-mm-aaaa"]') || document.getElementById('DateRangeStart');
            if (dateInput) {
              dateInput.value = appointmentDate;
              
              // Trigger change event
              const changeEvent = new Event('change', { bubbles: true });
              dateInput.dispatchEvent(changeEvent);
              return "Date set to " + appointmentDate;
            }
            return "Could not find date input";
          }, USER_CONFIG.appointmentDate);
        }).then(function(dateResult) {
          console.log(dateResult);
          
          // Set postal code from user configuration
          return page.evaluate(function(postalCode) {
            const postalCodeInput = document.getElementById('PostalCode');
            if (postalCodeInput) {
              postalCodeInput.value = postalCode;
              
              // Trigger change event
              const changeEvent = new Event('change', { bubbles: true });
              postalCodeInput.dispatchEvent(changeEvent);
              return "Postal code set to " + postalCode;
            }
            return "Could not find postal code input";
          }, USER_CONFIG.postalCode);
        }).then(function(postalResult) {
          console.log(postalResult);
          
          // Set search perimeter from user configuration
          return page.click('#perimeterCombo').then(function() {
            return page.keyboard.press(USER_CONFIG.searchPerimeter);
          });
        }).then(function() {
          return page.select('#consultingReason', USER_CONFIG.consultationReason); // Consultation urgente
        }).then(function() {
          //Random click
          return page.click('#consultingReasonTitle');
        }).then(function() {
          // Click the final Rechercher button to search for clinics
          return page.click('.h-SearchButton');
        }).then(function() {
          // Start the infinite loop to find the closest clinic
          return findClosestClinic(page);
        }).then(function(clinicResult) {
          console.log("Found clinic after " + clinicResult.attempts + " attempts: " + clinicResult.clinicName + " (" + clinicResult.distance + " km)");
          console.log("Appointment: " + clinicResult.appointment);
          console.log("SUCCESS: Appointment booked successfully!");
        });
    }).catch(function(error) {
      console.error('An error occurred:', error);
    });
  });
})();