(function(n,t,i){const r=90;i(t).ready(function(){function f(){var r=i(t.body);i.cookie("_ck_o")==null&&(r.addClass("non-interactive"),i("#utilisationTemoins, #paramTemoins, header").addClass("popup-interactive"));e(n)||i("#utilisationTemoins").removeClass("hide");i("[data-target='#paramTemoins'], [href='#paramTemoins']").on("click",function(){r.addClass("modal-open");o(n)});i("#btnConfirmSelect").on("click",function(){u();r.removeClass("modal-open");i("#utilisationTemoins").addClass("hide")});i("#btnToutAccepter, #btnToutAccepterParams").on("click",function(){u(!0);r.removeClass("modal-open");i("#utilisationTemoins").addClass("hide")});i("#btnToutAccepter, #btnToutAccepterParams, #btnConfirmSelect").on("click",function(){r.removeClass("non-interactive");i("#utilisationTemoins, #paramTemoins, header").removeClass("popup-interactive")})}function e(n){var t=!1;return i(n).each(function(){i.cookie(i(this).attr("name"))&&(t=!0)}),t}function u(n=false){i.cookie(i("#_ck_o").attr("name"),i("#_ck_o").val(),{expires:r,path:"/"});n?i.cookie(i("#_ck_p").attr("name"),i("#_ck_p").val(),{expires:r,path:"/"}):i("#_ck_p").prop("checked")?i.cookie(i("#_ck_p").attr("name"),i("#_ck_p").val(),{expires:r,path:"/"}):i.removeCookie(i("#_ck_p").attr("name"),{path:"/"})}function o(n){i(n).each(function(){i.cookie(i(this).attr("name"))&&i(this).prop("checked",!0)})}var n=i("#paramTemoins div.checkbox label :checkbox");f()})})(window,document,jQuery);
