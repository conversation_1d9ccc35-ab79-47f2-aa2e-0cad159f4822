#holder {
	width: 100%;
}

#holder > div {
	clear: both;
	padding: 2%;
	margin-bottom: 20px;
	border-bottom: 1px solid #eee;
	float: left;
	width: 96%;
}

.regular-checkbox {
	-webkit-appearance: none;
	background-color: #fafafa;
	border: 1px solid #cacece;
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05);
	padding: 9px;
	border-radius: 3px;
	display: inline-block;
	position: relative;
}

.regular-checkbox:active, .regular-checkbox:checked:active {
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);
}

.regular-checkbox:checked {
	background-color: #e9ecee;
	border: 1px solid #adb8c0;
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1);
	color: #99a1a7;
}

.regular-checkbox.blue:checked {
	background-color: #D9F0FF;
	border: 1px solid #649DFF;
	color: #649DFF;
}

.regular-checkbox:checked:after {
	content: '\2714';
	font-size: 14px;
	position: absolute;
	top: 0px;
	left: 3px;
	color: #99a1a7;
}

.regular-checkbox.blue:checked:after {
	color: #649DFF;
}

.big-checkbox {
	padding: 18px;
}

.big-checkbox:checked:after {
	font-size: 28px;
	left: 6px;
}

.verybig-checkbox {
	padding: 28px;
}

.verybig-checkbox:checked:after {
	font-size: 48px;
	left: 7px;
}

.regular-radio {
	-webkit-appearance: none;
	background-color: #fafafa;
	border: 1px solid #cacece;
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05);
	padding: 9px;
	border-radius: 50px;
	display: inline-block;
	position: relative;
}

.regular-radio:checked:after {
	content: ' ';
	width: 12px;
	height: 12px;
	border-radius: 50px;
	position: absolute;
	top: 3px;
	background: #99a1a7;
	box-shadow: inset 0px 0px 10px rgba(0,0,0,0.3);
	text-shadow: 0px;
	left: 3px;
	font-size: 32px;
}

.regular-radio:checked {
	background-color: #e9ecee;
	color: #99a1a7;
	border: 1px solid #adb8c0;
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1), inset 0px 0px 10px rgba(0,0,0,0.1);
}

.regular-radio:active, .regular-radio:checked:active {
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);
}

.big-radio {
	padding: 16px;
}

.big-radio:checked:after {
	width: 24px;
	height: 24px;
	left: 4px;
	top: 4px;
}




/**************/

.go-selectbutton {
	background-color: #fafafa;
	border: 1px solid #cacece;
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05);
	padding: 9px;
	border-radius: 3px;
	display: inline-block;
	position: relative;
	margin-right: 5px;
}

.go-selectbutton-radio {
	border-radius: 25px;
    text-align:left;
}

.go-selectbutton, .go-selectbutton:hover 
{
    text-decoration: none;
}

.go-selectbutton:active, .go-selectbutton.selected:active {
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px 1px 3px rgba(0,0,0,0.1);
}

.go-selectbutton.selected {
	background-color: #e9ecee;
	border: 1px solid #adb8c0;
	box-shadow: 0 1px 2px rgba(0,0,0,0.05), inset 0px -15px 10px -12px rgba(0,0,0,0.05), inset 15px 10px -12px rgba(255,255,255,0.1);
	color: #99a1a7;
}

.go-selectbutton.blue.selected {
	background-color: #D9F0FF;
	border: 1px solid #006dcc;
	color: #006dcc;
}

.go-selectbutton.selected:after {
	content: '\2714';
	font-size: 22px;
	position: absolute;
	top: -2px;
	left: 0px;
	color: #777;
}

.go-selectbutton-radio.selected:after {
	content: ' ';
	width: 12px;
	height: 12px;
	border-radius: 50px;
	position: absolute;
	top: 3px;
	background: #99a1a7;
	box-shadow: inset 0px 0px 10px rgba(0,0,0,0.3);
	text-shadow: 0px;
	left: 3px;
	font-size: 32px;
}

.go-selectbutton.blue.selected:after {
	color: #006dcc;
}

.go-selectbutton-big {
	padding: 18px;
}

.go-selectbutton-big.selected:after {
	font-size: 32px;
    left: 4px;
    top: 8px;
}

.go-selectbutton-verybig {
	padding: 28px;
}

.go-selectbutton-verybig.selected:after {
	font-size: 48px;
	left: 7px;
	top: 18px;
}
.go-selectbutton-group 
{
    text-align: left;
}
.go-selectbutton-group .checkbox
{
    text-align: left;
    margin: 5px;
    padding-left: 0;
}

.go-selectbutton-group .checkbox a
{
}

.go-selectbutton-group .checkbox span
{
    margin-left: 10px;
}
