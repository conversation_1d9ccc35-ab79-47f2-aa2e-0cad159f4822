/*!********************************************************************************************
This website and its content is copyright of RDVS - © RDVS Inc. 2012. All rights reserved.
Any redistribution or reproduction of part or all of the contents in any form is prohibited.
You may not, except with our express written permission, distribute or commercially exploit the content. 
Nor may you transmit it or store it in any other website or other form of electronic retrieval system.
***********************************************************************************************/

function decodeHtml(n){return $("<div />").html(n).text()}function isNotNull(n){return typeof n!="undefined"&&n!==null}function getUrlParam(n,t){n=n.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var r="[\\?&]"+n+"=([^&#]*)",u=new RegExp(r),i=u.exec(t||window.location.href);return i==null?"":i[1]}function RemoveParameterFromUrl(n){var t=window.location.href;return t.replace(new RegExp("[?&]"+n+"=[^&#]*(#.*)?$"),"$1").replace(new RegExp("([?&])"+n+"=[^&]*&"),"$1")}var delayExecute=function(){var n={};return function(t,i,r){clearTimeout(n[t]);n[t]=setTimeout(i,r)}}();$.fn.isOnScreen=function(){var i=$(window),t={top:i.scrollTop(),left:i.scrollLeft()},n;return t.right=t.left+i.width(),t.bottom=t.top+i.height(),n=this.offset(),n.right=n.left+this.outerWidth(),n.bottom=n.top+this.outerHeight(),!(t.right<n.left||t.left>n.right||t.bottom<n.top||t.top>n.bottom)};String.prototype.format=function(){for(var n=this,t=arguments.length;t--;)n=n.replace(new RegExp("\\{"+t+"\\}","gm"),arguments[t]);return n};String.prototype.formatArray=function(n){for(var t=this,i=n.length;i--;)t=t.replace(new RegExp("\\{"+i+"\\}","gm"),n[i]);return t};typeof String.prototype.splice!="function"&&(String.prototype.splice=function(n,t,i){return this.slice(0,n)+i+this.slice(n+Math.abs(t))});typeof String.prototype.startsWith!="function"&&(String.prototype.startsWith=function(n){return this.slice(0,n.length)==n});typeof String.prototype.endsWith!="function"&&(String.prototype.endsWith=function(n){return this.slice(-n.length)==n});typeof String.prototype.capitalize!="function"&&(String.prototype.capitalize=function(){return this.replace(/\w\S*/g,function(n){return n.charAt(0).toUpperCase()+n.substr(1).toLowerCase()})});typeof String.prototype.trim!="function"&&(String.prototype.trim=function(){return $.trim(this)});typeof String.prototype.replaceAll!="function"&&(String.prototype.replaceAll=function(n,t){var i=this;return i.replace(new RegExp(n,"g"),t)});jQuery.fn.redraw=function(){return this.hide(0,function(){$(this).show()})};Array.prototype.indexOf||(Array.prototype.indexOf=function(n,t){for(var i=t||0,r=this.length;i<r;i++)if(this[i]===n)return i;return-1});window.location.origin||(window.location.origin=window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:""));
function isTouchDevice(){return typeof ontouchstart!="undefined"?!0:!1}var console,Diag;typeof console=="undefined"&&(console={log:function(){}});window.onerror=function(n,t,i,r,u){if(!(n.indexOf("Script error.")>-1)){var f=t.substring(t.indexOf("?v=")+3);t=t.substring(0,t.indexOf("?v="));try{window.event!=null&&window.event.error!=null&&(n=n+(window.event.error.stack||""))}catch(e){}return Diag.log.fatal(n+" @ "+t+" line "+i+" column "+r+" StackTrace: "+u),!1}};Diag={trackUser:function(){},log:{trace:function(n,t){Diag.logger||Diag.init();Diag.logger.debug(Diag.format(n,t))},debug:function(n,t){Diag.logger||Diag.init();Diag.logger.debug(Diag.format(n,t))},info:function(n,t){Diag.logger||Diag.init();Diag.logger.info(Diag.format(n,t))},warning:function(n,t){Diag.logger||Diag.init();Diag.logger.warn(Diag.format(n,t))},error:function(n,t){Diag.logger||Diag.init();Diag.logger.error(Diag.format(n,t))},fatal:function(n,t){Diag.logger||Diag.init();Diag.logger.fatal(Diag.format(n,t))}},init:function(){typeof RDVS!="undefined"&&(RDVS.enableUserTracking=$("#EnableUserTracking").val()==="1");Diag.logger={debug:function(n){console!=null&&($.isFunction(console.debug)?console.debug(n):$.isFunction(console.log)&&console.log(n))},info:function(n){console!=null&&($.isFunction(console.info)?console.info(n):$.isFunction(console.log)&&console.log(n))},warn:function(n){console!=null&&($.isFunction(console.warn)?console.warn(n):$.isFunction(console.log)&&console.log(n))},error:function(n){console!=null&&($.isFunction(console.error)?console.error(n):$.isFunction(console.log)&&console.log(n))},fatal:function(n){console!=null&&($.isFunction(console.fatal)?console.fatal(n):$.isFunction(console.log)&&console.log(n))}}},format:function(n,t){if(t==null||typeof t!="array")return n;for(var i=0;i<t.length;i++)n=n.replace("{"+i+"}",t[i]);return n},toggleWindow:function(){Diag.inPageAppender.isVisible()?(Diag.inPageAppender.hide(),$("body").css("height","0px")):(Diag.inPageAppender.show(),isTouchDevice()||($("body").css("height","1200px"),$("body>div:last-child").css("position","fixed")))}};$(document).ready(function(){Diag.init();$(document).on("keypress",function(n){n.shiftKey&&(n.keyCode==10||n.keyCode==13)&&Diag.toggleWindow()});Diag.log.debug("Diag properly initilized")});
var goSessionStore={initialized:!1,storage:null,init:function(){this.initialized||(this.storage==null&&(this.usingSessionStorage=this.isSessionStorageSupported()),this.initialized=!0)},setItem:function(n,t){this.init();try{this.usingSessionStorage?sessionStorage.setItem(n,t):$.cookie(n,t,{path:"/"})}catch(i){}},getItem:function(n){this.init();try{return this.usingSessionStorage?sessionStorage.getItem(n):$.cookie()[n]}catch(t){return""}},clear:function(){this.usingSessionStorage&&sessionStorage.clear()},clearSwitchCompagnie:function(){var n=!1;typeof sessionStorage.getItem("sawNotifications")!="undefined"&&(n=sessionStorage.getItem("sawNotifications"));var t=sessionStorage.getItem("Username"),i=sessionStorage.getItem("userProfile"),r=sessionStorage.getItem("LdapId");this.init();this.usingSessionStorage&&(sessionStorage.clear(),sessionStorage.setItem("sawNotifications",n),sessionStorage.setItem("Username",t),sessionStorage.setItem("userProfile",i),sessionStorage.setItem("LdapId",r))},isSessionStorageSupported:function(){try{var n="testincognito",t=window.sessionStorage;return t.setItem(n,"1"),t.removeItem(n),!0}catch(i){return!1}},areCookiesEnabled:function(){document.cookie="__verifyCookiesEnabled=1";var n=document.cookie.length>=1&&document.cookie.indexOf("__verifyCookiesEnabled=1")!==-1,t=new Date(1976,8,16);return document.cookie="__verifyCookiesEnabled=1;expires="+t.toUTCString(),n}};
var labels={},filterState="off",RDVS={navigationInterceptor:{},isAvailableFor:{All:-1,LCGA:1,Cliniques:2,Assure:4,Nurse811:8,CentreHospitaliers:16,IntervenantClinique:32,Intervenant911:64,IntervenantGap:128},covid19Segment:{Aucune:-1,CentreDepistage:1,CDE:2},dataServices:{dataApiUrl:""},pageInfo:{rootUrl:""},externals:{Modernizr:typeof Modernizr!="undefined"?Modernizr:null,Underscore:typeof _!="undefined"?_:null,Backbone:typeof Backbone!="undefined"?Backbone:null},gotoPricingPage:function(){window.top.location=RDVS.pageInfo.rootUrl+"pricing/"},showPrepaidAppointmentBank:function(){$.pnotify_remove_all();setTimeout(function(){RDVS.pageHeader.element.find(".h-AccountInformationButton").toggleClass("open")},500)},sessionInfo:{setLastShowBandeauId:function(n){goSessionStore.setItem("LastShowBandeauId",n)},getLastShowBandeauId:function(){return goSessionStore.getItem("LastShowBandeauId")},setShowBandeau:function(n){goSessionStore.setItem("ShowBandeau",n)},getShowBandeau:function(){return goSessionStore.getItem("ShowBandeau")},setInterfaceConnect:function(n){goSessionStore.setItem("InterfaceConnect",n)},getInterfaceConnect:function(){return goSessionStore.getItem("InterfaceConnect")},setLdapId:function(n){goSessionStore.setItem("LdapId",n)},getLdapId:function(){return goSessionStore.getItem("LdapId")},setIsLinkedToCompany:function(n){goSessionStore.setItem("linkedToCompany",n)},getIsLinkedToCompany:function(){return goSessionStore.getItem("linkedToCompany")},setGateWayDirection:function(n){goSessionStore.setItem("gatewayDirection",n)},getGateWayDirection:function(){return goSessionStore.getItem("gatewayDirection")},setProfile:function(n){goSessionStore.setItem("userProfile",n)},getProfile:function(){return goSessionStore.getItem("userProfile")},isGatewayAvailable:function(){var n=goSessionStore.getItem("userProfile");return goSessionStore.getItem("IsSupport")==="true"?!0:n=="3"||n>="5"||(n=="1"||n=="2"||n=="4")&&goSessionStore.getItem("linkedToCompany")?!0:!1},setIsAdmin:function(n){goSessionStore.setItem("IsAdmin",n)},getIsAdmin:function(){return goSessionStore.getItem("IsAdmin")==="true"},setIsSupport:function(n){goSessionStore.setItem("IsSupport",n)},getIsSupport:function(){return goSessionStore.getItem("IsSupport")==="true"},setCompanyId:function(){},getCompanyId:function(){return getSessionUser().get("companyId")},filterIsOn:function(){return this.getSelectedResources().length>1},getCompany:function(){return model.companies.get(this.getCompanyId())},setUserId:function(n){this.setUserJustLoggedIn(!0);goSessionStore.setItem("UserId",n)},getModel:function(){return model},getActorEmployee:function(){return this.getCompany()?this.getCompany().getEmployee(getSessionUser().id):null},set811SearchPostalCode:function(n){goSessionStore.setItem("QuickSearchPostalCode",n)},get811SearchPostalCode:function(){return goSessionStore.getItem("QuickSearchPostalCode")},loginAs:function(n){this.setUserJustLoggedIn(!1);this.setSelectedResource({type:"user",id:n});goSessionStore.setItem("UserId",n)},getUserId:function(){var n=parseInt(goSessionStore.getItem("UserId"),10);return(isNaN(n)||n==0)&&(n=parseInt($("#RDVSUserId").val(),10)),n},setBookingForDependent:function(){goSessionStore.setItem("BookingForDependent",!0)},getBookingForDependent:function(){return goSessionStore.getItem("BookingForDependent")==="true"},setRedirectInfo:function(n){goSessionStore.setItem("RedirectInfo",n)},getRedirectInfo:function(){return goSessionStore.getItem("RedirectInfo")},getSelectedResources:function(){var n=goSessionStore.getItem("SelectedResources"),t;return n!=null&&n!="undefined"&&n.length?(t=JSON.parse(n),index=0,_.each(t,function(n){n.index=index++}),t):[{type:"user",id:this.getUserId()}]},resourceAlreadyExist:function(n,t){return _.filter(n,function(n){return n.id===t}).length>0},setSelectedResources:function(n){goSessionStore.setItem("SelectedResources",JSON.stringify(n))},addSelectedResource:function(n){var t=this.getSelectedResources();_.where(t,{type:n.type,id:n.id}).length==0&&t.push(n);this.setSelectedResources(t)},removeSelectedResource:function(n){var t=this.getSelectedResources(),i=_.where(t,{type:n.type,id:n.id});i.length>0&&t.splice(t.indexOf(i[0]),1);this.setSelectedResources(t)},replaceSelectedResource:function(n,t){var i=this.getSelectedResources(),r=_.where(i,{type:t.type,id:t.id});r.length>0?i[i.indexOf(r[0])]=n:this.resourceAlreadyExist(i,n.id)||i.push(n);this.setSelectedResources(i)},clearSelectedResources:function(){this.setSelectedResources([])},setSelectedResource:function(n){this.setSelectedResources([n])},getSelectedResource:function(){var n=this.getSelectedResources();return n.length>0?n[0]:null},isResourceSelected:function(n){var t=this.getSelectedResources();return _.where(t,n).length!=0},getVisibleResources:function(){for(var f=this.getSchedulesPagingIndex(),t=this.getMaxSchedulesToDisplay(),i=f*t,e=i+t-1,r=this.getSelectedResources(),u=[],n=i;n<=e&&n<r.length;n++)u.push(r[n]);return u},isResourceVisible:function(n){var t=this.getVisibleResources();return _.where(t,n).length!=0},isUserScheduleVisible:function(n){var t=model.users.get(n);return t!=null&&t.get("responsibleUserId")!=0?this.getVisibleScheduleUserIds().indexOf(t.get("responsibleUserId"))>=0:this.getVisibleScheduleUserIds().indexOf(n)>=0},getProfessionalPageIndex:function(n){var t=this.getSelectedResources(),i=this.getMaxSchedulesToDisplay(),r=_.chain(t).pluck("id").indexOf(n).value()+1,u=Math.ceil(r/i);return u-1},getVisibleScheduleUserIds:function(){var n=[],t=this.getVisibleResources();return _.chain(t).where({type:"user"}).each(function(t){n.push(t.id)}),n},getVisibleScheduleUserId:function(){var n=this.getVisibleScheduleUserIds();return n.length>0?n[0]:0},setSelectedUser:function(n){this.clearSelectedResources();this.setSelectedResource({type:"user",id:n})},getVisibleRoomIds:function(){var n=[],t=this.getVisibleResources();return _.chain(t).where({type:"room"}).each(function(t){n.push(t.id)}),n},isRoomVisible:function(n){return this.getVisibleRoomIds().indexOf(n)>=0},getVisibleEquipmentIds:function(){var n=[],t=this.getVisibleResources();return _.chain(t).where({type:"equipment"}).each(function(t){n.push(t.id)}),n},isEquipmentVisible:function(n){return this.getVisibleEquipmentIds().indexOf(n)>=0},schedulesPagingIndex:0,getSchedulesPagingIndex:function(){return this.schedulesPagingIndex||0},setSchedulesPagingIndex:function(n){n<0&&(n=0);this.schedulesPagingIndex=n||0},getMaxSchedulesToDisplay:function(n){if(Common.isMobileDevice())return 1;var t=panelManager.mainView.panel.width();return n=n||panelManager.schedule.settings.viewType,n===model.constants.viewType.day?t<=320?1:t<=710?2:t<=930?3:t<=1150?4:t<=1370?5:t>1590?6:t>1810?7:t>2400?10:5:1},isEmployeeWithoutScheduleSelected:function(){var n=this.getSelectedResource();return!n||n.id==null||!RDVS.sessionInfo.getCompany().getEmployee(n.id).hasSchedule},setUserJustLoggedIn:function(n){goSessionStore.setItem("UserJustLoggedIn",n)},getUserJustLoggedIn:function(){return goSessionStore.getItem("UserJustLoggedIn")=="true"},setOfflineModeEnabled:function(){},getOfflineModeEnabled:function(){return!1},setUsername:function(n){goSessionStore.setItem("Username",n)},getUsername:function(){return goSessionStore.getItem("Username")},isValid:function(){var n=this.getUserId();return!(isNaN(n)||n==null)},clear:function(){goSessionStore.clear()},logout:function(n){function t(){return goSessionStore.clear(),window.top.location=RDVS.pageInfo.rootUrl+"Signin/",!0}if(Common.isClientSection())return goSessionStore.clear(),window.top.location=RDVS.pageInfo.rootUrl+"prendrerendezvous/",!0;var i=window.getSessionUser!=null?getSessionUser():null;i==null||isNaN(i.id)||n!==undefined&&n!=!1?t():Common.callDataApiMethod({type:"POST",path:"account/signout",onSuccess:t,onError:t})},logoutClient:function(){goSessionStore.clear();window.top.location=RDVS.pageInfo.rootUrl+"Signin/"},getEmployeeLogged:function(){return this.getCompany().getEmployee(getSessionUser().id)},getSelectedEmployee:function(){var t=null,n=this.getSelectedResources().filter(function(n){return n.type==="user"});return n.length>0&&(n=n[0].id,t=this.getCompany().getEmployee(n)),t}},init:function(){RDVS.pageInfo=JSON.parse($("#RDVSPageInfo").val());RDVS.dataServices=JSON.parse($("#RDVSDataServices").val());RDVS.csrfToken=$("#RDVSCSRFToken").val();RDVS.enableUserTracking=$("#EnableUserTracking").val()==="1"}},AccessLevel={READ_ONLY:0,READ_WRITE:1,BLOCK:2},Security={sanitizeWysiwygContent:function(n,t){Common.callDataApiMethod({type:"POST",path:"util/sanitize",data:{value:n},onSuccess:function(n){t(n)},onError:function(){t()}})},doesEmployeeHaveReadWriteAccessLevel:function(n,t){var u=RDVS.sessionInfo.getCompany(),i=u.getEmployees(!0,AccessLevel.READ_ONLY),r;return i=_.sortBy(i,function(n){return RDVSUIHelper.removeNonAscii(n.fullName)}),r=AccessLevel.BLOCK,_.each(i,function(i){Security.findAccessLevelOfScheduleForEmployee(n,i,t)==AccessLevel.READ_WRITE&&(r=AccessLevel.READ_WRITE)}),r==AccessLevel.READ_WRITE},findAccessLevelOfScheduleForEmployee:function(n,t,i){var u=this,r=AccessLevel.BLOCK,f,o,e;return n.id===t.id||n.permissions.isAdmin||n.permissions.canManageResources?r=AccessLevel.READ_WRITE:i?(f=u.findLocationForCompanyAddressId(n.CompanyEmployeeLocations,i),o=u.findLocationForCompanyAddressId(t.CompanyEmployeeLocations,i),f!==null&&o!==null&&(n.accessLevelOfColleaguesScheduleForAllLocations<AccessLevel.BLOCK?r=n.accessLevelOfColleaguesScheduleForAllLocations:f.AccessLevelOfColleaguesSchedule<AccessLevel.BLOCK?r=f.AccessLevelOfColleaguesSchedule:(e=n.ScheduleManagementPermission.filter(function(n){return n.companyEmployeeLocation.companyAddressId===parseInt(i)&&n.companyEmployeeLocation.companyEmployeeId===t.id}),e.length===1&&(r=e[0].accessLevel)))):n.CompanyEmployeeLocations.forEach(function(i){var f=u.findAccessLevelOfScheduleForEmployee(n,t,i.companyAddressId);u.accessLevelIsWiderThan(f,r)&&(r=f)}),r},findLocationForCompanyAddressId:function(n,t){if(!n)return null;var i=n.filter(function(n){return n.companyAddressId===parseInt(t)});return i.length>0?i[0]:null},accessLevelIsWiderThan:function(n,t){return n===1||n===0&&t===2},RW:1,RO:0,BLOCK:2};
function copyToClipboard(n){window.prompt("Copy to clipboard: Ctrl+C, Enter",n)}function parseDate(n,t,i){var u,s,o,f,e,h,c,l,r;for(t=t||"yyyy-MM-dd",i=i||"-",u=n.split("T"),s=u[0],u.length>1&&(o=u[1]),f=t.split(i),e=s.split(i),r=0;r<3;r++)f[r]=="yyyy"?h=parseInt(e[r]):f[r]=="MM"?c=parseInt(e[r])-1:f[r]=="dd"&&(l=parseInt(e[r]));if(o!=null){var a=o.split(":"),v=parseInt(a[0]),y=parseInt(a[1]);return new Date(h,c,l,v,y,0,0)}return null}function formatDate(n,t,i,r,u){return RDVSUIHelper.formatDate(n,t,i,r,u)}function getMonthName(n,t,i){return RDVSUIHelper.getMonthName(n,t,i)}function formatWithPadding(n,t){return RDVSUIHelper.formatWithPadding(n,t)}function consume_alert(){if(!consumedAlert){consumedAlert=window.alert;var n=null;window.alert=function(t,i){n!=null&&(n.pnotify_remove(),n=null);n=Common.notifyMessage(t,i)}}}function showBrowserRecommendation(){var n=navigator.userAgent.toLowerCase().indexOf("chrome")>-1,t=navigator.userAgent.toLowerCase().indexOf("safari")>-1;n||t?$(".h-BrowserRecommendation").hide():($(".h-BrowserRecommendation").show(),$(".h-InstallBrowser").click(function(n){n.preventDefault();window.open($(this).attr("href"))}))}function setWYSIWYGContent(n,t){var i=tinymce.get(n);if(i.initialized)i.setContent(t);else i.on("init",function(n){n.target.setContent(t||"")})}function getWYSIWYGContentAsPlainText(n){return tinymce.triggerSave(),tinyMCE.editors[n].getContent({format:"text"})}function getWYSIWYGContent(n){return tinymce.triggerSave(),$("#"+n).val()}function closeAccordion(n){$(".h-goAccordionContent",n).each(function(){var n=$(this).is(":visible");n&&$(this).parent().find(".h-goAccordionTitle").click()})}function openFirstAccordion(n){var t=$(".h-goAccordionContent:first",n),i=t.is(":visible");openAccordionGroup($(".h-goAccordionTitle",t.parent()))}function openAccordionGroup(n){var t=null,i,r;n.hasClass("h-goAccordionTitle")?(t=n,n=t.closest(".h-goAccordionGroup")):t=n.closest(".h-goAccordionTitle");i=$(".h-goAccordionContent",n);r=i.is(":visible");r||t.click()}function openAccordion(n,t){openAccordionGroup($(n,t))}function displayError(n,t){var i,r;$("#{0}Button".format(t),"#{0}".format(t)).popover("show");i=$("#{0}Error".format(t)).closest(".popover");i.addClass("error");$(".popover-content",i).html(Common.format("<p>{0}<\/p>",[n]));r=setTimeout(function(){clearTimeout(r);$("#{0}Button".format(t),"#{0}".format(t)).popover("hide")},1e4)}function iOSversion(){if(/(iPhone|iPod|iPad)/i.test(navigator.userAgent)){var n=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);return[parseInt(n[1],10),parseInt(n[2],10),parseInt(n[3]||0,10)]}}function getDateOfLastSunday(n){var t=new Date(n.getTime());return t.setHours(0,0,0,0),t.setDate(t.getDate()-n.getDay()),t}function getDateOfNextSunday(n){var t=new Date(n.getTime());return t.setHours(0,0,0,0),t.setDate(t.getDate()-n.getDay()+7),t}var Common={AfficherMessageRvsq:function(n){var v=RDVS.sessionInfo.getShowBandeau(),u=RDVS.sessionInfo.getLastShowBandeauId(),o=n.numero!==u;if((u===""||u===undefined)&&(o=!1),v==="true"||o){RDVS.sessionInfo.setShowBandeau(!0);RDVS.sessionInfo.setLastShowBandeauId(n.numero);var s=!0,y=document.documentElement?document.documentElement.lang:"",h=getUrlParam("culture")||y,t=n.message&&n.message.texteFormatHtml?n.message.texteFormatHtml:n.message&&n.message.texte?n.message.texte:n.description;h&&h.toLowerCase().includes("en")&&(t=n.message&&n.message.texteFormatHtmlAng?n.message.texteFormatHtmlAng:n.message&&n.message.texteAng?n.message.texteAng:t);t=t.replace(/^<p>/,"").replace(/<\/p>$/,"");t=t.replace(/<a(?![^>]*\btarget=)/g,'<a target="_blank"');$("#messagebandeaurvsq").html(t);var f="bandeaurvsq-unplanned",c=f,s=!0,i=$("#warningIcon"),r=$("#infoIcon"),e=$("#bandeaurvsq"),l=i.find("path"),a=r.find("path");n.typeInterruption=="2"&&(c=f,s=!0);s?(i.show(),r.hide()):(i.hide(),r.show());this.removeClasseByElement([l,a,i,r,e],"bandeaurvsq-planned",f);this.addClasseByElement([l,a,i,r,e],c);e.show()}},removeClasseByElement:function(n,...t){n.forEach(n=>{t.forEach(t=>{n.removeClass(t)})})},addClasseByElement:function(n,t){n.forEach(n=>{n.addClass(t)})},FermerBandeau:function(){$("#bandeaurvsq").hide();$("#messagebandeaurvsq").html("");RDVS.sessionInfo.setShowBandeau(!1)},FetchInterruption:function(){var t=RDVS.sessionInfo.getInterfaceConnect(),i=t==="0"?"SEL-RV-C":"SEL-RV-P",r="Interruption/cms-interruption/"+i,n=this;this.callDataApiMethod({type:"GET",path:r,data:{},onSuccess:function(t){if(t.length>0){let i=t.filter(function(n){var t=Date.now(),i,r;return n.etat===2?(i=n.dateDebutDiffusionMessage?n.dateDebutDiffusionMessage:n.periode&&n.periode.dateDebut?n.periode.dateDebut:n.periode.dateDebutPrevue,r=n.periode&&n.periode.dateFin?n.periode.dateFin:n.periode.dateFinPrevue,n.typeInterruption===1?Date.parse(i)<=t&&Date.parse(r)>=t?!0:!1:Date.parse(i)<=t?!0:!1):!1});if(i.length>0){let t=i.sort(function(n,t){var u=n.dateDebutDiffusionMessage?n.dateDebutDiffusionMessage:n.periode&&n.periode.dateDebut?n.periode.dateDebut:n.periode.dateDebutPrevue,f=t.dateDebutDiffusionMessage?t.dateDebutDiffusionMessage:t.periode&&t.periode.dateDebut?t.periode.dateDebut:t.periode.dateDebutPrevue,i=new Date(u),r=new Date(f);return i.getTime()!==r.getTime()?r-i:t.numero-n.numero});n.AfficherMessageRvsq(t[0])}}},onError:function(){n.FermerBandeau()}})},getServiceDurationDescription:function(n){var t=Math.floor(n/60),r=n%60,i="";return t>=1&&(i="{0} {1}".format(t,t>1?"heures":"heure")),r>=1&&(t>=1&&(i+=" et "),i+="{0} {1}".format(r,r>1?"minutes":"minute")),i},isAppOnline:function(){return!0},isClientSection:function(){return window.location.href.toLowerCase().indexOf("/prendrerendezvous/")>-1||window.location.href.toLowerCase().indexOf("/assure/")>-1},checkDomain:function(n){return n.indexOf("//")===0&&(n=location.protocol+n),n.toLowerCase().replace(/([a-z])?:\/\//,"$1").split("/")[0]},isExternal:function(n){return(n.indexOf(":")>-1||n.indexOf("//")>-1)&&this.checkDomain(location.href)!==this.checkDomain(n)},setConnectionStatus:function(){},formatRefNumber:function(n){return n?"{0} {1} {2}".format(n.substring(0,4),n.substring(4,8),n.substring(8)):""},trackEvent:function(n,t,i,r,u,f){if(isNotNull(n)){var e=[];e.push("_trackEvent");e.push(t);e.push(i);isNotNull(r)&&e.push(r);isNotNull(u)&&e.push(u);isNotNull(f)&&e.push(f);n.push(e)}},trackSignupConversions:function(){try{isNotNull(_gaq)&&RDVS.enableUserTracking===!0&&$("body").append('<iframe width="0" height="0" border="none" src="/GoogleAdwordsConversion_Trial.html"><\/iframe>')}catch(n){}},trackAppointmentBookingConversions:function(){try{isNotNull(_gaq)&&RDVS.enableUserTracking===!0&&$("body").append('<iframe width="0" height="0" border="none" src="/GoogleAdwordsConversion_ApptBooking.html"><\/iframe>')}catch(n){}},trackUserAction:function(n,t,i){var u=new TimeSpan(Date.now()-t),r=0;u!=null&&(r=u.getMilliseconds());i!=null?Diag.trackUser(n+" : {0}ms : error={1}".format(r,i)):Diag.trackUser(n+" : {0}ms".format(r))},callDataApiMethod:function(n){n=$.extend({type:"GET",path:"",data:{},contentType:null,onSuccess:null,onError:null},n);n.data.ajaxTimeStamp=(new Date).getTime();var t=function(t,i,r){if(Common.hideWaiting(),r==="Unauthorized"||r==="NoActiveSession"){Common.isClientSection()?Common.notifyError(labels.ErrorMessage_SessionExpired_Client||"Session Lost"):Common.notifyError(labels.ErrorMessage_SessionExpired||"Session Lost");var u=setTimeout(function(){RDVS.sessionInfo.logout(!0)},3e3)}setTimeout(function(){if(isNotNull(t)&&isNotNull(t.responseText)&&t.responseText.length!==0&&Diag.log.error(t.responseText),typeof n.onError!="function"||!n.onError(t,i,r))Common.onAjaxPageMethodError(t,i,r)},200);Common.setConnectionStatus(!1)},i=function(t){if(Common.setConnectionStatus(!0),Common.hideWaiting(),t!=null&&t.error!=null)if(t.error==="Unauthorized"||t.error=="NoActiveSession"){Common.isClientSection()?Common.notifyError(labels.ErrorMessage_SessionExpired_Client||"Session Lost"):Common.notifyError(labels.ErrorMessage_SessionExpired||"Session Lost");var i=setTimeout(function(){RDVS.sessionInfo.logout(!0)},3e3)}else t.errorCode==="70"?(Common.notifyError(t.error),setTimeout(function(){window.top.location="/QuickSearch"},3e3)):typeof n.onError=="function"&&n.onError(null,t.error,t)||(t.requiredValidation||Common.notifyError(t.error),$(".h-ClinicSelection").hide());else if(n.onSuccess!=null)n.onSuccess(t)},r={url:RDVS.dataServices.dataApiUrl+n.path,data:JSON.stringify(n.data||{}),type:n.type,contentType:n.contentType||"application/json; charset=utf-8",dataType:n.dataType||"json",cache:!1};Common.showWaiting(n.showWaiting);$.ajax(r).done(i).fail(t)},setAjaxHeaders:function(n){RDVS.csrfToken==""&&(RDVS.csrfToken=$("#RDVSCSRFToken").val());n.setRequestHeader("X-CSRF-Token",RDVS.csrfToken);n.setRequestHeader("go-userid",RDVS.sessionInfo.getUserId());var t=new Date,i=new Date(t.getTime()+t.getTimezoneOffset()*6e4);n.setRequestHeader("go-timestamp",i.toString("yyyy-MM-ddTHH:mm:ss"))},onAjaxPageMethodError:function(n,t,i){if(Common.hideWaiting(),i==="Unauthorized")throw n.responseText;else n!=null&&n.responseText!=""&&n.status!==0?(Common.notifyError(n.responseText),console.log(n.responseText)):(Common.notifyError(labels.ErrorMessage_AjaxError),console.log("onAjaxPageMethodError: "+t))},timeoutDialog:null,resetSessionTimer:function(){$.timeoutDialog!=null&&RDVS.pageInfo.sessionTimeout>0&&(typeof this.timeoutDialog=="undefined"||this.timeoutDialog===null?this.timeoutDialog=$.timeoutDialog({timeout:RDVS.pageInfo.sessionTimeout,countdown:60,logout_redirect_url:"javascript:RDVS.sessionInfo.logout();",keep_alive_url:RDVS.pageInfo.rootUrl+"Account/KeepAlive.aspx",restart_on_yes:!0,title:labels.Message_SessionAboutToExpire,message:labels.Message_SessionAboutToExpireTimeRemaining,question:labels.Message_SessionAboutToExpireQuestion,keep_alive_button_text:labels.Message_SessionAboutToExpireStaySignedIn,sign_out_button_text:labels.Message_SessionAboutToExpireSignOut}):this.timeoutDialog.keepAlive(!1))},reloadWindow:function(n,t){var i,r;n!=null?(i=unescape(window.location.pathname),t!=null&&(i+=window.location.search),r=(new Date).getTime(),i=window.location.search.length>0?i+"&t"+r:i+"?t"+r,window.location=i):window.location=window.location},if0:function(n,t){return n==0?t:n},encodeHtml:function(n){return $("<div />").text(n).html()},decodeHtml:function(n){return $("<div />").html(n).text()},stripHtml:function(n){return $("<div />").html(n).text()},format:function(n,t){typeof t!="object"&&(t=[t]);for(var i=0;i<t.length;i++)n=n.replace("{"+i+"}",t[i]);return n},removeTimeFromDate:function(n){return new Date(n.getTime()).setHours(0,0,0,0)},readFile:function(n,t){var i=new FileReader;i.onload=function(n){t(n.target.result)};i.readAsDataURL(n)},setDateAsUTC:function(n){return new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()))},cleanDate:function(n,t){return t===!1?(n.setHours(0),n.setMinutes(0)):n.setMinutes(Math.floor(n.getMinutes()/15)*15),n.setSeconds(0),n.setMilliseconds(0),n},getDaysBetweenDates:function(n,t){var i=n.getTime(),r=t.getTime(),u=Math.abs(i-r);return Math.round(u/864e5)},translateDay:function(n){return RDVSUIHelper.translateDay(n)},validateTimeString:function(n){n.length===4&&(n="0"+n);return/^(00|01|02|03|04|05|06|07|08|09|10|11|12|13|14|15|16|17|18|19|20|21|22|23)[:](0|1|2|3|4|5)\d{1}$/.test(n)},timeStringToMinutes:function(n){var r,i,t;return n=n.toLowerCase(),r=!1,n.indexOf("pm")>=0&&(r=!0),n=n.replace("pm","").replace("am",""),i=n.split(":"),t=parseInt(i[0],10)*60+(i.length>0?parseInt(i[1],10):0),isNaN(t)?0:(r&&(t=t+720),t)},validateEmail:function(n){return RDVSUIHelper.validateEmail(n)},onBadPhoneNumber:function(n,t){Common.notifyError(labels.ErrorMessage_PhoneNumberIsInvalid,!0,{hide:!0,delay:5e3});isNotNull(t)&&t.closest(".control-group").addClass("error")},validatePhoneNumber:function(n,t){var r=$("#"+n.attr("data-phoneinput"),n.parent()),u=r.val(),i;return u.trim()!=""&&(i=n.val(),i.trim()=="")?(t!==!1&&this.notifyError(labels.Message_CountryCodeIsMandatory),!1):!0},setPhoneNumber:function(n,t,i){n.val(t||"1");i!=null&&i.startsWith("1")&&t=="1"&&(i=i.substring(1));$("#"+n.attr("data-phoneinput"),n.parent()).val(i);this.setPhoneCountryCode(n)},isKnownCountryCode:function(n){return["1","32","33","49","31","41"].indexOf(n)>=0},getPhoneMask:function(){return"unmaskedvalue"},setPhoneCountryCode:function(n){var t=$("#"+n.attr("data-phoneinput"),n.parent());t.inputmask(this.getPhoneMask(n.val()))},displayOperationDoneAlert:function(n){n=$.extend({container:$("body"),timeout:5e3,classes:""},n);n.container.find(".h-OperationDoneAlert.alert").remove();var t=$('<div class="h-OperationDoneAlert operationDoneAlert alert alert-info inline-block {1}">{0}<\/div>'.format(labels.Label_Done,n.classes));t.appendTo(n.container);setTimeout(function(){t.hide("slow");setTimeout(function(){t.remove()},2e3)},n.timeout)},formatPhone:function(n,t,i,r){var o,l,f,e,s,h,a;if(t==null||(r=r||function(){},n=n||"1",t=Common.unformatPhone($.trim(t)),t===""))return"";n==="1"&&t.length===11&&(t=t.substring(1));var v=!0,u=n+t,c=_.find([{code:"1",format:"1 (BBB) BBB-BBBB",region:"North-America"},{code:"32",format:"32 BBB BB BB BB",region:"Belgium"},{code:"33",format:"33 BB BB BB BB BB",region:"France"},{code:"49",format:"49 BBBB BBBBBB",region:"Germany"},{code:"31",format:"31 BB BBBBBBBB",region:"Netherlands"},{code:"41",format:"41 AA BBB BB BB",region:"Switzerland"}],function(t){return n===t.code});if(c!=null){for(o=[],l=c.format,f=0;f<l.length;f++)e=l[f],e!="+"&&isNaN(parseInt(e))&&e!="B"&&o.push({pos:f,character:e});for(s=0;s<o.length;s++)h=o[s],u.length>=h.pos&&(u=u.splice(h.pos,0,h.character));a=c.format.length;v=u.length===a;u=u.substring(0,a)}if(v)isNotNull(i)&&i.closest(".control-group").removeClass("error");else return r(u,i),u;return u},unformatPhone:function(n){return n.replace(/[^+0-9.]/g,"")},formatNumber:function(n,t){return t=t||" ",Math.max(0,n).toFixed(0).replace(/(?=(?:\d{3})+$)(?!^)/g,t)},modalOverlay:null,showModalOverlay:function(n){this.modalOverlay&&(this.modalOverlay.remove(),this.modalOverlay=null);var t={opacity:"0.8","z-index":"1000",display:"none",position:"fixed",top:"0",bottom:"0",right:"0",left:"0"};n!=null&&(t={opacity:"0.8","z-index":"1000",display:"none",position:"absolute",top:n.position().top,bottom:n.position().top+n.height(),right:n.position().left+n.width(),left:n.position().left});this.modalOverlay=$("<div />",{"class":"ui-widget-overlay",css:t}).appendTo(n||"body").fadeIn("fast");this.modalOverlay.click(function(n){n.stopPropagation()})},hideModalOverlay:function(){this.modalOverlay!=null&&this.modalOverlay.fadeOut("fast")},displayMap:function(n){window.open(n)},typeTo:function(n,t,i,r){function u(n,t,f,e){setTimeout(function(){if(f.length<t.length){var r=t[e];f=f+r;n.val(f);u(n,t,f,e+1)}else $.isFunction(i)&&i()},r||300)}n.focus();u(n,t,"",0)},stripHtmlTag:function(n){return n.replace(/.*"*?<[^>]*>*"*/gm,"")},displayMessage:function(n){bootbox.dialog(n,[{label:labels.Button_Close,"class":"btn",callback:function(){}}])},savedNotification:null,displayNotification:function(n,t){var i=t||"success";this.savedNotification!=null&&this.savedNotification.pnotify_remove();this.savedNotification=$.pnotify({title:n,history:!1,type:i,icon:"fa fa-check",delay:3e3,animation:{effect_in:"slide",effect_out:"slide"},animate_speed:"fast",addclass:"stack-center savedNotfication"},!1)},displaySavedNotification:function(){this.displayNotification(labels.Label_Done)},notify:function(n,t,i){t=t||{};t.icon=t.icon||"fa fa-exclamation-circle";t.animate_speed=t.animate_speed!=null?t.animate_speed:700;t.sticker=t.sticker!=null?t.sticker:!0;t.closer_hover=t.closer_hover!=null?t.closer_hover:!1;t.addclass=t.addclass||"stack-center";t.history=t.history!=null?t.history:!1;t.delay=t.delay||5e3;t.hide=t.hide!=null?t.hide:t.type!="error";t.type=t.type||"info";t.title=t.title||"";t.text=n;i!==!0&&$.pnotify_remove_all();var r=$.pnotify(t);return r.click(function(n){n.stopPropagation()}),r},notifyMessage:function(n,t,i){if(this.isMobileDevice()||$("body").width()<=600)return bootbox.dialog(n,[{label:labels.Button_Close,"class":"btn",callback:function(){}}]),null;t=t||{};t.mouse_reset=!1;t.delay=t.delay||6e4;t.addclass="stack-center";t.animation={effect_in:"slide",effect_out:"slide"};t.animate_speed=t.animate_speed||"fast";t.before_open=function(n){n.css({top:$(window).height()/2-n.height()/2})};var r=Common.notify('<br/><p class="center">{0}<\/p><p class="center"><a href="javascript:;" class="btn h-ButtonClose">{1}<\/a><\/p>'.format(n,labels.Button_Close),t,i);return r.find(".h-ButtonClose").click(function(n){n.preventDefault();r.pnotify_remove()}),r},notifyWithYesNoButtons:function(n){var t=$.pnotify({type:n.showAsError?"error":"info",title:n.title||" ",addclass:"stack-center",text:'<p class="center">{0}<\/p><p class="center"><a href="javascript:;" class="btn h-ButtonYes">{1}<\/a>&nbsp;<a href="javascript:;" class="btn h-ButtonNo">{2}<\/a><\/p>'.format(n.message,n.yesButtonLabel,n.noButtonLabel),icon:!1,hide:!1,closer:!1,sticker:!1,mouse_reset:!1,history:!1,insert_brs:!1});t.find(".h-ButtonYes").click(function(i){i.preventDefault();t.pnotify_remove();$.isFunction(n.onYesButtonClicked)&&n.onYesButtonClicked()});t.find(".h-ButtonNo").click(function(i){i.preventDefault();t.pnotify_remove();$.isFunction(n.onNoButtonClicked)&&n.onNoButtonClicked()})},displayErrorMessage:function(n,t){var r=RDVS.sessionInfo.getGateWayDirection(),f=window.location.pathname,e=r=="CH"||r=="811"||r=="INT"||r=="911"||r=="GAP"||f=="/Schedule/",o=n.indexOf(labels.ErrorMessage_ServerError_part1)>=0,i="",u;e?(i='<div class="message">{0}<\/div>'.format(labels.Label_technicalPb),i+='<div> <p class="fa fa-exclamation-triangle fa-4x" style="color:#ffcc00;" ><\/p><\/div>',i+='<div class="message">{0}<\/div>'.format(n),i+='<div class="buttons">     <a class="btn h-CloseButton" onclick="document.getElementById(\''+t+'\').focus();" href="javascript:;">{0}<\/a>'.format(labels.Button_Close)+"<\/div>"):(n="",n=labels.ErrorMessage_ServerError_Citoyen,i='<div class="message">{0}<\/div>'.format(labels.Label_error),i+='<div> <p class="fa fa-exclamation-triangle fa-4x" style="color:#ffcc00;" ><\/p><\/div>',i+='<div class="message">{0}<\/div>'.format(n),i+='<div class="buttons">'+'     <a class="btn h-CloseButton" href="javascript:;">{0}<\/a>'.format(labels.Button_TryAgain)+"<\/div>");u=$.pnotify({type:"error",width:"500px",title:"",addclass:"notificationMessage",text:i,icon:!1,sticker:!1,closer:!1,hide:!1,mouse_reset:!1,history:!1,insert_brs:!1,animate_speed:400,animation:{effect_in:"fade",effect_out:"fade"},before_open:function(){Common.showModalOverlay()},before_close:function(){Common.hideModalOverlay()}});u.click(function(n){n.stopPropagation()});u.find(".h-CloseButton").focus();u.find(".h-CloseButton").click(function(n){n.preventDefault();n.stopPropagation();u.pnotify_remove()})},notifyError:function(n){if(this.isMobileDevice()||$("body").width()<=600)bootbox.dialog(n,[{label:labels.Button_Close,"class":"btn",callback:function(){}}]);else{var t="";Array.isArray(n)&&n.length>1?(t="<ul>",_.each(n,function(n){t+='<li class="errorPopUp">{0}<\/li>'.format(n)}),t+="<\/ul>"):t=Array.isArray(n)&&n.length===1?n[0]:n;this.displayErrorMessage(t)}},notifyErrorSetFocusTo:function(n,t){if(this.isMobileDevice()||$("body").width()<=600)bootbox.dialog(n,[{label:labels.Button_Close,"class":"btn",callback:function(){}}]);else{var i="";Array.isArray(n)&&n.length>1?(i="<ul>",_.each(n,function(n){i+='<li class="errorPopUp">{0}<\/li>'.format(n)}),i+="<\/ul>"):i=Array.isArray(n)&&n.length===1?n[0]:n;this.displayErrorMessage(i,t)}},formatDurationToLabel:function(n){var t="",i=Math.floor(n/60),r=n%60;return i>0&&(t+=i===1?"1 heure":i+" heures"),r>0&&(i>0&&(t+=" "),t+=r===1?"1 minute":r+" minutes"),t},notiftyWarning:function(n,t){return t=t||{},t.delay=t.delay||1e4,t.type="warning",Common.notify(n,t)},waitingSpinner:null,waitingDialog:null,waitingDepth:0,showWaiting:function(n){this.waitingDepth++;this.waitingDepth==1&&setTimeout(function(n,t){n.waitingDepth>0&&n.showWaitingNow(t)},300,this,n)},hideWaiting:function(){this.waitingDepth--;this.waitingDepth==0&&setTimeout(function(n){n.waitingDepth==0&&n.hideWaitingNow()},300,this)},showWaitingNow:function(n){if($("body").css("cursor","wait"),$("body").append('<div id="greyCover"style="opacity: 0.8; z-index: 1100; position: fixed; top: 0px; bottom: 0px; right: 0px; left: 0px; display: none; background-color:#CECCCC;"><\/div>'),$("#greyCover").show(),this.waitingSpinner==null){this.waitingSpinner=new Spinner({color:"#333",top:"50%",left:"50%"});this.waitingSpinner.spin();this.waitingDialog=$('<div class="waitingDialog">{0}<\/div>'.format(typeof n=="string"?n:""));var t=$(".h-WaitingDialogHost");t.length==0&&(t=$("body"));this.waitingDialog.appendTo(t);$(this.waitingSpinner.el).appendTo(this.waitingDialog);$(this.waitingDialog).children().append('<div class="loading" aria-label="'+labels.Message_Loading+'">'+labels.Message_Loading+"<\/div>");$(".loading").show()}else this.waitingSpinner.spin(),$(this.waitingSpinner.el).appendTo(this.waitingDialog),this.waitingDialog.show(),$(this.waitingDialog).children().append('<div class="loading" aria-label="'+labels.Message_Loading+'">'+labels.Message_Loading+"<\/div>"),$(".loading").show()},hideWaitingNow:function(){$("body").css("cursor","default");$("#greyCover").hide();$("#greyCover").remove();$(".loading").hide();$(".loading").remove();this.waitingSpinner!=null&&(this.waitingSpinner.stop(),this.waitingDialog.hide())},scrollToId:function(n,t,i){this.scrollToElement($("#"+n),t,i)},scrollToElement:function(n,t,i){n!=null&&n.length>0&&(t=$(t||"body"),i=$(i||t),t.stop().animate({scrollTop:n.offset().top-i.offset().top},"slow"))},scrollElementToCenter:function(n,t,i){if(n!=null&&n.length>0){t=$(t||"body");i=$(i||t);var r=n.offset().top-i.offset().top,u=$(window).height()/2;isNaN(u)||(r=r-u+50);t.stop().animate({scrollTop:r},"slow")}},initDatePicker:function(n,t){$.fn.datepicker.dates.en={days:[labels.Sunday,labels.Monday,labels.Tuesday,labels.Wednesday,labels.Thursday,labels.Friday,labels.Saturday,labels.Sunday],daysShort:[labels.Sunday.substring(0,3),labels.Monday.substring(0,3),labels.Tuesday.substring(0,3),labels.Wednesday.substring(0,3),labels.Thursday.substring(0,3),labels.Friday.substring(0,3),labels.Saturday.substring(0,3),labels.Sunday.substring(0,3)],daysMin:[labels.Sunday.substring(0,2),labels.Monday.substring(0,2),labels.Tuesday.substring(0,2),labels.Wednesday.substring(0,2),labels.Thursday.substring(0,2),labels.Friday.substring(0,2),labels.Saturday.substring(0,2),labels.Sunday.substring(0,2)],months:[labels.January,labels.February,labels.March,labels.April,labels.May,labels.June,labels.July,labels.August,labels.September,labels.October,labels.November,labels.December],monthsShort:[labels.January.substring(0,3),labels.February.substring(0,3),labels.March.substring(0,3),labels.April.substring(0,3),labels.May.substring(0,3),labels.June.substring(0,3),labels.July.substring(0,3),labels.August.substring(0,3),labels.September.substring(0,3),labels.October.substring(0,3),labels.November.substring(0,3),labels.December.substring(0,3)],today:labels.Today,clear:labels.Button_Clear};t=$.extend({keyboardNavigation:Common.isMobileDevice()==!1,todayBtn:"linked",todayHighlight:!0,autoclose:!0,disableTouchKeyboard:Common.isMobileDevice(),orientation:"top",enableOnReadonly:!1,multidate:1},t);var i=n.datepicker(t);if(n.parent().find(".add-on .icon-calendar").click(function(){$(n).prop("disabled")===!1&&n.datepicker("show")}),!isTouchDevice())n.on("keydown",function(n){n.keyCode==13&&i.blur()});return i},initTimePicker:function(n,t){if($.extend({disableFocus:!0,showWidgetOnAddonClick:!0},t),n.timepicker(t),!isTouchDevice())n.on("keydown",function(n){n.keyCode==13&&picker.blur()});return n.timepicker()},getSelect2Settings:function(){return{formatNoMatches:function(){return labels.Select2_NoMatches},formatInputTooShort:function(n,t){var i=t-n.length;return labels.select2Labels.inputTooShort.format(i,i==1?"":"s")},formatInputTooLong:function(n,t){var i=n.length-t;return labels.select2Labels.inputTooLong.format(i,i==1?"":"s")},formatSelectionTooBig:function(){return labels.select2Labels.selectionTooBig.format(n,n==1?"":"s")},formatLoadMore:function(){return labels.select2Labels.loadMore},formatSearching:function(){return labels.select2Labels.searching}}},isMobileDevice:function(){return RDVS.pageInfo.isMobileDevice},isDevice:function(){return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substr(0,4))},setReadOnly:function(n,t){$("input",t).prop("readonly",n===!0);$("input",t).prop("disabled",n===!0);n===!0?$("select",t).attr("disabled",n===!0):$("select",t).removeAttr("disabled");$("select > option",t).prop("disabled",n===!0);$("textarea",t).prop("readonly",n===!0);$(".btn, button",t).prop("disabled",n===!0)},Pad:function(n){return n<10?"0"+n.toString():n.toString()}},consumedAlert=null,JsonFormatter={stringify:function(n){var t={ct:n.ciphertext.toString(CryptoJS.enc.Base64)};return n.iv&&(t.iv=n.iv.toString()),n.salt&&(t.s=n.salt.toString()),JSON.stringify(t)},parse:function(n){var t=JSON.parse(n),i=CryptoJS.lib.CipherParams.create({ciphertext:CryptoJS.enc.Base64.parse(t.ct)});return t.iv&&(i.iv=CryptoJS.enc.Hex.parse(t.iv)),t.s&&(i.salt=CryptoJS.enc.Hex.parse(t.s)),i}};if($(document).ready(function(){var r=this,t,i,n;$.ajaxSetup({beforeSend:function(n){Common.setAjaxHeaders(n)}});$("input[type=text], input[type=email], input[type=password], input[type=number], input[type=checkbox], input[type=radio]").on("keypress",function(n){n.which==13&&n.preventDefault()});showBrowserRecommendation();consume_alert();Common.setConnectionStatus();$(".h-goAccordion").each(function(){$(this).goAccordion()});t=navigator.userAgent.toLowerCase().indexOf("chrome")>-1;i=navigator.userAgent.toLowerCase().indexOf("safari")>-1;(t||i)&&$(".backButton").addClass("withArrow");n=$(".h-PhoneCountryCode");n.length>0&&(n.tooltip(),n.change(function(){Common.setPhoneCountryCode($(this))}));$("a").each(function(){var n=$(this).attr("href");n&&Common.isExternal(n)&&$(this).attr("id")!="returnHome"&&$(this).attr("target","_blank")})}),function(n,t){function i(n,t){if(n!=null){var f=n.closest(".h-goAccordion"),u=f.attr("data-id"),i=n.closest(".h-goAccordionGroup"),r=i.hasClass("open");return f.attr("data-singleOpen")!="false"&&f.find(".h-goAccordionGroup").removeClass("open"),t!==!0&&r?(t===!1||r)&&i.removeClass("open"):i.addClass("open"),i.hasClass("open")?(r=!0,(u||"")!=""&&(GOAccordions.accordionStates[u]={openedGroup:n})):(r=!1,(u||"")&&(GOAccordions.accordionStates[u]={openedGroup:null})),Common.scrollToElement(i.find(".h-goAccordionBottom")),r}}t.fn.goAccordion=function(n){n=$.extend({singleOpen:!0},n);$(this).addClass("goAccordion");$(this).attr("data-singleOpen",n.singleOpen);$(this).find(".h-goAccordionGroup").each(function(){$(this).goAccordionGroup(n)})};t.fn.goAccordionGroup=function(n){n=$.extend({onOpen:function(){},onClose:function(){}},n);$(this).addClass("accordionGroup");$(this).find(".h-goAccordionTitle").addClass("accordionTitle");$(this).find(".h-goAccordionContent").addClass("accordionContent");$('<i class="icon icon-chevron-down"><\/i>').insertAfter($(this).find(".h-goAccordionTitle > .icon.icon-chevron-right"));var t=$(this),r={toggle:function(){if(!this.locked){var r=i(t);r?n.onOpen():n.onClose()}},open:function(){i(t,!0);n.onOpen()},close:function(){i(t,!1);n.onClose()},locked:!1,lock:function(){this.locked=!0},unlock:function(){this.locked=!1}};return $(this).find(".h-goAccordionTitle").click(function(){r.toggle()}),r};n.GOAccordions={accordionStates:[],applyAccordionLastStatus:function(n){var t=n.attr("data-id");t!=null&&t!=""&&this.accordionStates[t]!=null&&i(this.accordionStates[t].openedGroup,!0)}}}(window,jQuery),function(n,t){jQuery.fn.goTabs=function(n){n=t.extend({},n);var i={init:function(n){var i=this;i.element=n;n.find(".h-MainTab").click(function(){i.showTab(t(this).attr("data-name"))})},showTab:function(n){var t=this,i=this.element.find(".h-MainTab[data-name={0}]".format(n));RDVS.navigationInterceptor.onClickNavigationLink(function(){t.element.find(".h-MainTab").removeClass("active");i.addClass("active");t.element.find(".h-MainTabPanel").hide();var r=t.element.find(".h-MainTabPanel[data-name={0}]".format(n));r.show()});return i}};return i.init(t(this)),i}}(window,jQuery),function(){var n=jQuery.event.special,t="D"+ +new Date,i="D"+(+new Date+1);n.scrollstart={setup:function(){var i,r=function(t){var r=this,u=arguments;i?clearTimeout(i):(t.type="scrollstart",jQuery.event.handle.apply(r,u));i=setTimeout(function(){i=null},n.scrollstop.latency)};jQuery(this).bind("scroll",r).data(t,r)},teardown:function(){jQuery(this).unbind("scroll",jQuery(this).data(t))}};n.scrollstop={latency:300,setup:function(){var t,r=function(i){var r=this,u=arguments;t&&clearTimeout(t);t=setTimeout(function(){t=null;i.type="scrollstop";jQuery.event.handle.apply(r,u)},n.scrollstop.latency)};jQuery(this).bind("scroll",r).data(i,r)},teardown:function(){jQuery(this).unbind("scroll",jQuery(this).data(i))}}}(),$.fn.goCollapsibleText===undefined){$.fn.goCollapsibleText=function(t){var r=$(this),i;t=t||250;i=r.text();i!=null&&i.length>t&&n(r,i,t)};function t(t,i,r){t.html(i+' <a href="javascript:;" class="h-Less">[-]<\/a>');$(".h-Less",t).click(function(){n(t,i,r)})}function n(n,i,r){n.html(i.substring(0,r)+' <a href="javascript:;" class="h-More">[...]<\/a>');$(".h-More",n).click(function(){t(n,i,r)})}}Array.prototype.findIndex||(Array.prototype.findIndex=function(n){"use strict";var t;if(this==null)throw new TypeError("Array.prototype.findIndex called on null or undefined");if(typeof n!="function")throw new TypeError("predicate must be a function");var i=Object(this),u=i.length>>>0,f=arguments[1],r;for(t=0;t<u;t++)if(r=i[t],n.call(f,r,t,i))return t;return-1});
var RDVSUIHelper={getIEVersion:function(){var n=navigator.userAgent.toLowerCase();return n.indexOf("msie")!=-1?parseInt(n.split("msie")[1]):!1},createOption:function(n,t){var i=document.createElement("option");return i.value=n,i.text=t,i},formatCulture:function(n){return n.toLowerCase()==="fr-ca"?"fr":"en"},addClassIfNotExists:function(n,t){_.contains(n.classList,t)||n.classList.add(t)},getUserMesssageWhenOnlineBookingNotPossible:function(n,t,i,r){var s,h;if(!r&&i&&t!=null&&(t.denyOnlineBookingToNewClientsMessage||"").trim()!="")return t.denyOnlineBookingToNewClientsMessage;var u=n!=null?n.name:"",e=!1,f="",o="";if(t!=null){if(!r&&i&&(s=t.denyOnlineBookingToNewClientsMessage||"",s!=""))return s;u=t.fullName;(t.workNumber||"")!=""?(f=this.formatPhone(t.workNumberCountryCode,t.workNumber)+((t.workNumberExtension||"")!=""?" x "+t.workNumberExtension:""),e=!0):(t.email||"")!=""&&(o=t.email,e=!0)}return e?f!=""?i?labels.Message_MustCallProfessionalForFirstAppointment.format(f,u):labels.Message_MustCallProfessionalForAppointment.format(f,u):i?labels.Message_MustEmailProfessionalForFirstAppointment.format(o,u):labels.Message_MustEmailProfessionalForAppointment.format(o,u):n!=null&&(n.phoneNumber||"")!=""?(h=this.formatPhone(n.phoneNumberCountryCode,n.phoneNumber)+((n.phoneNumberExtension||"")!=""?" x "+n.phoneNumberExtension:""),i?labels.Message_MustCallProfessionalForFirstAppointment.format(h,u):labels.Message_MustCallProfessionalForAppointment.format(h,u)):i?labels.Message_MustEmailProfessionalForFirstAppointment.format(n.email,u):labels.Message_MustEmailProfessionalForAppointment.format(n.email,u)},validateEmailControl:function(n){var t=n.val();return t==""||this.validateEmail(t)?(n.removeClass("animated bounce"),n.parent().find(".h-EmailError").hide(),!0):(n.addClass("animated bounce"),n.parent().find(".h-EmailError").show(),!1)},validateEmail:function(n){return/^(([^<>()[\]\\.,;:%#*|&\s@\"]+(\.[^<>()[\]\\.,;:%#*|&\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(n)},validateOnlyDate:function(n,t){if(t&&(n==null||n==""))return!0;return!n.trim().match(/^(0?[1-9]|[12][0-9]|3[01])[\/\-](0?[1-9]|1[012])[\/\-]\d{4}$/)&&n.length!=10?!1:!0},validateDate:function(n,t){if(!this.validateOnlyDate(n,t))return!1;try{var r=n.substring(0,2),u=n.substring(3,5),f=n.substring(6,10),e=new Date(f,u-1,r,0,0,0),i=new Date;return e>i.setYear(i.getFullYear()+10)?!1:!0}catch(o){return!1}},validateEmails:function(n){var i=this,t=[];return n.indexOf(";")>=0?t=n.split(";"):n.indexOf(",")>=0?t=n.split(","):t.push(n),_.chain(t).map(function(n){return n.replaceAll('"',"").replaceAll("'","").trim()}).filter(function(n){return(n||"")!=""&&i.validateEmail(n)}).value().join(";")},renderDuration:function(n,t){var i=new Date(0);i.setHours(0,0,0,0);i.addMinutes(t);n.timepicker("setTime",i)},getDuration:function(n){var t=parseAnyDate(n.val());return t.getHours()*60+t.getMinutes()},onChangeDuration:function(n,t){var i=this;$.isFunction(t)&&n.change(function(){t(i.getDuration(n))})},fillDurationSelect:function(n,t,i){var r,u;if(n!=null||n.length>1)for(r=5;r<=(t||1440);r+=i||5)u=r===0?1:r,n.append($("<option><\/option>").val(r).text(this.formatDuration(u)))},formatDuration:function(n){var t=Math.floor(n/60),i=n%60,r=i>1?"s":"",u=t>1?"s":"";return t===0?labels.Label_DurationValueMinutes.format(t,i,u,r):t>0&&i===0?labels.Label_DurationValueHours.format(t,i,u,r):t>0&&i>0?labels.Label_DurationValueHoursMinutes.format(t,i,u,r):void 0},formatPrice:function(n){var i="",n=Math.round(n*100)/100,t=n-Math.floor(n);return t=Math.round(t*100)/100,i=t>0?t.toString().length==4?n.toString():n.toString()+"0":n.toString()+".00",labels.Label_MoneyAmount.format(i)},formatPhone:function(n,t,i){var e,c,u,f,o,s,l;if((t||"")==""||(n=n||"1",t=this.unformatPhone($.trim(t)),t===""))return"";n==="1"&&t.length===11&&(t=t.substring(1));var a=!0,r=t,h=_.find([{code:"1",format:"BBB BBB-BBBB",region:"North-America"},{code:"32",format:"32 BBB BB BB BB",region:"Belgium"},{code:"33",format:"33 BB BB BB BB BB",region:"France"},{code:"49",format:"49 BBBB BBBBBB",region:"Germany"},{code:"31",format:"31 BB BBBBBBBB",region:"Netherlands"},{code:"41",format:"41 AA BBB BB BB",region:"Switzerland"}],function(t){return n===t.code});if(h!=null){for(e=[],c=h.format,u=0;u<c.length;u++)f=c[u],f!="+"&&isNaN(parseInt(f))&&f!="B"&&e.push({pos:u,character:f});for(o=0;o<e.length;o++)s=e[o],r.length>=s.pos&&(r=r.splice(s.pos,0,s.character));l=h.format.length;a=r.length===l;r=r.substring(0,l)}return(i||"")!=""&&(r=r+" x "+i),r},unformatPhone:function(n){return n.replace(/[^+0-9.]/g,"")},getDayLabel:function(n,t){var i=this.translateDay(n);return t==!0?i:i.substring(0,3)+"."},formatProfessionalTitle:function(n){var t;return t=typeof n.practiceNumber!="undefined"?" - "+n.practiceNumber:typeof n.licenseNo!="undefined"?" - "+n.licenseNo:"",n.firstName+" "+n.lastName+t},getDateLabel:function(n,t,i){if(t===!0)return this.getDayLabel(n.getDay(),t===!0);var r=this.formatDate(n," ",i!==!1,!0,!1);return this.getDayLabel(n.getDay())+" "+r},formatTimestamp:function(n,t){return document.documentElement.lang==="fr"?"{0} {1} {2} @ {3}".format(n.getDate(),this.getMonthName(n.getMonth(),!0),n.getFullYear(),n.toString(t)):"{0} {1}, {2} @ {3}".format(this.getMonthName(n.getMonth(),!0),n.getDate(),n.getFullYear(),n.toString(t))},formatLongDateWithDay:function(n){var t="";return t=document.documentElement.lang==="fr"?n.getDate()+" "+this.getMonthName(n.getMonth(),!0).toLowerCase():this.getMonthName(n.getMonth(),!0)+" "+n.getDate(),this.translateDay(n.getDay(),!0)+", "+t+" "+n.getFullYear()},formatDate:function(n,t,i,r,u,f,e,o){t=t||"/";var s=this.formatWithPadding(n.getMonth()+1,2)+t+this.formatWithPadding(n.getDate(),2);return r===!0&&(s=document.documentElement.lang==="fr"?n.getDate()+" "+this.getMonthName(n.getMonth(),f,o).toLowerCase():this.getMonthName(n.getMonth(),f,o)+" "+n.getDate()),i===!0&&(s=r===!0?s+t+n.getFullYear():n.getFullYear()+t+s),u===!0&&(s=this.translateDay(n.getDay(),e)+t+s),s.trim()},formatWithPadding:function(n,t){return t==2?n<10?"0"+n:n:(t-=n.toString().length,t>0)?new Array(t+(/\./.test(n)?2:1)).join("0")+n:n},getMonthName:function(n,t,i){abbrStr='<abbr title="{0}">{1}<\/abbr>';var r="";switch(n){case 0:r=t?labels.January:i?abbrStr.format(labels.January,labels.January_Short):labels.January_Short;break;case 1:r=t?labels.February:i?abbrStr.format(labels.February,labels.February_Short):labels.February_Short;break;case 2:r=t?labels.March:i?abbrStr.format(labels.March,labels.March_Short):labels.March_Short;break;case 3:r=t?labels.April:i?abbrStr.format(labels.April,labels.April_Short):labels.April_Short;break;case 4:r=t?labels.May:i?abbrStr.format(labels.May,labels.May_Short):labels.May_Short;break;case 5:r=t?labels.June:i?abbrStr.format(labels.June,labels.June_Short):labels.June_Short;break;case 6:r=t?labels.July:i?abbrStr.format(labels.July,labels.July_Short):labels.July_Short;break;case 7:r=t?labels.August:i?abbrStr.format(labels.August,labels.August_Short):labels.August_Short;break;case 8:r=t?labels.September:i?abbrStr.format(labels.September,labels.September_Short):labels.September_Short;break;case 9:r=t?labels.October:i?abbrStr.format(labels.October,labels.October_Short):labels.October_Short;break;case 10:r=t?labels.November:i?abbrStr.format(labels.November,labels.November_Short):labels.November_Short;break;case 11:r=t?labels.December:i?abbrStr.format(labels.December,labels.December_Short):labels.December_Short;break;default:r="???"}return r},translateDay:function(n){return n==="Sunday"||n===0?labels.Sunday:n==="Monday"||n===1?labels.Monday:n==="Tuesday"||n===2?labels.Tuesday:n==="Wednesday"||n===3?labels.Wednesday:n==="Thursday"||n===4?labels.Thursday:n==="Friday"||n===5?labels.Friday:n==="Saturday"||n===6?labels.Saturday:void 0},getAge:function(n){return n==null?-1:(typeof n=="string"&&(n=Date.parse(n)),n!=null&&n.getTime()<=Date.today()?new TimePeriod(Date.parse(n),Date.today()).years:-1)},getAgeInMonths:function(n){if(n==null)return-1;if(typeof n=="string"&&(n=Date.parse(n)),n!=null&&n.getTime()<=Date.today()){var t=new TimePeriod(Date.parse(n),Date.today());return t.years*12+t.months}return-1},getAgeLabel:function(n){var t=this.getAge(n);return t>=0?t<2?(t=this.getAgeInMonths(n),t==1?labels.Label_AgeMonth.format(t):labels.Label_AgeMonths.format(t)):t==1?labels.Label_AgeYear.format(t):labels.Label_AgeYears.format(t):""},getCancelLimitText:function(n,t){var u=0,i,f,r,e;return n>=1&&n<=59?(i=t==="fr"||t==="French"?n===1?"minute":"minutes":n===1?"minute�s":"minutes�",u=n+" "+i):n>=60&&n<=4379?(r=Math.ceil(n/60),i=t==="fr"||t==="French"?r===1?"heure":"heures":r===1?"hour":"hours",u=r+" "+i):n>=4380&&(f=Math.floor(n/1440),i=t==="fr"||t==="French"?"jours":"days",u=f+" "+i,r=Math.ceil((n-f*1440)/60),r>0&&(t==="fr"||t==="French"?(i=r===1?"heure":"heures",e="et"):(i=r===1?"hour":"hours",e="and"),u=u+" "+e+" "+r+" "+i)),u},getGoogleMapLink:function(n){if(n==null)return"";return"https://www.google.ca/maps/place/{0}+{1}+{2}+{3}+{4}+{5}".format(n.streetNumber||"",n.streetName!=null?"+"+n.streetName:"",n.city!=null?"+"+n.city:"",n.stateOrProvince!=null?"+"+n.stateOrProvince:"",n.country!=null?"+"+n.country:"",n.postalCode!=null?"+"+n.postalCode:"")},getAddressLink:function(n){if(n==null)return"";var t=this.getAddressLabel(n),i=this.getGoogleMapLink(n);return'<div><a href="{1}" target="_blank" title="{2}">{0}<\/a><\/div>'.format($.trim(t),$.trim(i),labels.Accessibility_OpensInANewWindow)},getAddressLabel:function(n){if(n==null||n.streetName==null)return"";var t="{0} {1}".format(n.streetNumber,n.streetName).trim();return n.unitNumber&&(t+=", {0} {1}".format(labels.Label_Suite,n.unitNumber).trim()),t+", {0} ({1})&nbsp;&nbsp;{2}".format(n.city,n.stateOrProvince,n.postalCode).trim()},initCheckboxActivatedSection:function(n,t){t=$.extend({change:function(){}},t);var i={init:function(){var t=this;return n.bootstrapSwitch({onText:labels.Yes,offText:labels.No,size:"normal",labelWidth:"0px",handleWidth:"40px",onSwitchChange:function(n,i){t._toggle(i,!0)}}),n.closest(".h-CheckboxActivatedSection").find(".bootstrap-switch").attr("title",n.attr("title")),this},_toggle:function(i,r){var u=n.closest(".h-CheckboxActivatedSection");return i?u.find(".h-Section").show():u.find(".h-Section").hide(),t.change(i,u,r),this},setState:function(t){return n.bootstrapSwitch("state",t,!0),this._toggle(t,!1),this},setState:function(t,i){return n.bootstrapSwitch("state",t,!0),this._toggle(t,i),this},setReadOnly:function(t){return n.bootstrapSwitch("readonly",t),this}};return i.init(),i},initSideTabs:function(n,t){t=$.extend({onShowTab:function(){}},t);var i={init:function(){var t=this;n.find(".h-SideTabs > li > a").click(function(n){n.preventDefault();var i=this;RDVS.navigationInterceptor.onClickNavigationLink(function(){$(i).attr("disabled")!="disabled"&&t.showTab($(i).attr("data-panel"))})})},showTab:function(i){n.find(".h-Panel").hide();n.find(".h-SideTabs > li").removeClass("active");n.find("li > a[data-panel={0}]".format(i)).parent().addClass("active");n.find(".h-Panel.h-"+i).show();t.onShowTab(i);panelManager.contextualHelp.setAccountSettingsSideTab(i)},changeFieldsColor:function(){$("input").css("background-color","red");$("select").css("background-color","red");$("textarea").css("background-color","red")},setTabDisabled:function(t,i){i?n.find("li > a[data-panel={0}]".format(t)).attr("disabled","disabled"):n.find("li > a[data-panel={0}]".format(t)).removeAttr("disabled")},setTabVisibility:function(t,i){i?n.find("li > a[data-panel={0}]".format(t)).show():n.find("li > a[data-panel={0}]".format(t)).hide()}};return i.init(),i},renderTable:function(n,t){try{n.fnClearTable();n.fnAddData(t,!0)}catch(i){Diag.log.error(i)}},initDataTable:function(n,t,i,r){function o(n,t,r){i.onRenderRowEditor(n,t,r);n.find(".h-SaveButton").click(function(e){e.preventDefault();i.onSaveRow(n,t,function(n){n!=null&&(f($(r)),u.fnDeleteRow(r),u.fnAddData(n),RDVS.navigationInterceptor.resetFormsChangedState())})});t.id?(n.find(".h-DeleteButton").show(),n.find(".h-DeleteButton").click(function(n){n.preventDefault();i.onDeleteRow(t,function(n){n&&(f($(r)),u.fnDeleteRow(r),RDVS.navigationInterceptor.resetFormsChangedState())})})):n.find(".h-DeleteButton").hide();n.find(".h-CancelButton").click(function(e){e.preventDefault();var o=u.fnSettings().bCloseRowOnCancel;if(o)f(n.closest("tr")),n.closest("tr").remove(),(t.id||0)==0?u.fnDeleteRow(r):f($(r));else i.onRenderRowEditor(n,t,r);RDVS.navigationInterceptor.resetFormsChangedState()})}function s(t,i,r){var u=$(n.find(".h-TableRowDetails").html());return u.attr("data-id",i.id),o(u,i,r),u}function f(n){var t=n.get(0);u.fnIsOpen(t)&&(n.removeClass("active"),u.fnClose(t),$("body").stop().animate({scrollTop:0},"fast"))}var e=this,u;return jQuery.fn.dataTableExt.oSort["string-pre"]=function(n){return e.removeNonAscii(n.toString().toLowerCase())},i=$.extend({onRowClosed:function(){},onRenderRow:function(){},onCreateRow:function(){return null},onRenderRowEditor:function(){},onSaveRow:function(){return!1},onDeleteRow:function(){return!1}},i),t=$.extend({oLanguage:labels.datatableLabels,aoColumns:[{sTitle:"",mDataProp:"id"},],aaSorting:[[0,"asc"]],bPaginate:!1,bFilter:!1,bCloseRowOnCancel:!1,bInfo:!1,fnCreatedRow:function(n,t,r){$(n).attr("data-id",t.id);$(n).click(function(){var r=$(this).get(0);RDVS.navigationInterceptor.onClickNavigationLink(function(){if($("tr.active").each(function(){this!=r&&(u.fnClose(this),$(this).removeClass("active"));$(this).find("[id^='addId_']").addClass("caret-right")}),u.fnIsOpen(r)){$(n).removeClass("active");t.id=="0"?u.fnDeleteRow(n):u.fnClose(r);i.onRowClosed(u,t,n)}else u.fnOpen(r,s(u,t,n),"details"),$(n).addClass("active")})});u.find("tr").removeAttr("data-isLastRowAdded");$(n).attr("data-isLastRowAdded","true");i.onRenderRow($(n),t,r);return n}},t),u=n.find(r||"table.h-MainTable").dataTable(t),n.find(".h-AddButton").click(function(n){RDVS.navigationInterceptor.onClickNavigationLink(function(){n.preventDefault();var t=i.onCreateRow();t!=null&&(u.fnAddData(t),u.find("tr").each(function(){f($(this))}),u.find("tr[data-isLastRowAdded=true]").click())})}),u},addHtmlInFrame:function(n,t){var i=document.createElement("iframe"),r;i.id="RDVSFrame";i.width="300px";i.height="80px";i.frameBorder="0";r='<!DOCTYPE html><head><title>RDVS Dynamic iframe<\/title><\/head><body style="margin:0; padding-left: 0; padding-right: 0; background-color: transparent; overflow: hidden;">'+t+"<\/body><\/html>";n.get(0).appendChild(i);i.contentWindow.contents=r;i.src='javascript:window["contents"]'},onMenuButtonClick:function(n){n!=null&&$(n).closest(".btn-group").removeClass("open")},getDateWithoutTime:function(n){var t=new Date(n.getTime());return t.setHours(0),t.setMinutes(0),t.setSeconds(0),t.setMilliseconds(0),t},initRemoveNonAscii:function(){var n,i,t;try{for(this.defaultDiacriticsRemovalap=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"OE",letters:"Œ"},{base:"oe",letters:"œ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],this.diacriticsMap={},n=0;n<this.defaultDiacriticsRemovalap.length;n++)for(i=this.defaultDiacriticsRemovalap[n].letters,t=0;t<i.length;t++)this.diacriticsMap[i[t]]=this.defaultDiacriticsRemovalap[n].base}catch(r){Diag.log.error(r);this.diacriticsMap=null}},charToUnicode:function(n){for(var t=n.charCodeAt(0).toString(16).toUpperCase();t.length<4;)t="0"+t;return"\\u"+t},removeNonAscii:function(n){var t=this;return t.diacriticsMap!=null?n.replace(/[^\u0000-\u007E]/g,function(n){return t.diacriticsMap[n]||n}):n},iOSversion:function(){if(/iP(hone|od|ad)/.test(navigator.platform)){var n=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);return[parseInt(n[1],10),parseInt(n[2],10),parseInt(n[3]||0,10)]}}};RDVSUIHelper.initRemoveNonAscii();
(function(n,t){t(document).ready(function(){t("body").makeDropdownSubmenusOpenOnClick()});t.fn.makeDropdownSubmenusOpenOnClick=function(){t(this).find(".dropdown-submenu").click(function(n){n.stopPropagation();t(this).find(".dropdown-submenu").removeClass("open");t(this).parents(".dropdown-submenu").addClass("open");t(this).toggleClass("open");t(this).siblings().removeClass("open")});t(this).find(".dropdown > a").click(function(){t(this).parent().find(".dropdown-submenu, .dropdown").removeClass("open")});t(this).supportMultipleDropdownsInSameBtnGroup()};t.fn.supportMultipleDropdownsInSameBtnGroup=function(){t(this).find(".btn.dropdown").click(function(n){n.stopPropagation();t(this).parent().toggleClass("open")})}})(window,jQuery);
(function(n,t){function i(){return RDVS.enableUserTracking?(n.mixpanel==null&&(function(t,i){if(!i.__SV){var r,f,e,u;n.mixpanel=i;i._i=[];i.init=function(n,t,r){function o(n,t){var i=t.split(".");2==i.length&&(n=n[i[0]],t=i[1]);n[t]=function(){n.push([t].concat(Array.prototype.slice.call(arguments,0)))}}var f=i;for("undefined"!=typeof r?f=i[r]=[]:r="mixpanel",f.people=f.people||[],f.toString=function(n){var t="mixpanel";return"mixpanel"!==r&&(t+="."+r),n||(t+=" (stub)"),t},f.people.toString=function(){return f.toString(1)+".people (stub)"},e="disable track track_pageview track_links track_forms register register_once alias unregister identify name_tag set_config people.set people.set_once people.increment people.append people.track_charge people.clear_charges people.delete_user".split(" "),u=0;u<e.length;u++)o(f,e[u]);i._i.push([n,t,r])};i.__SV=1.2;r=t.createElement("script");r.type="text/javascript";r.async=!0;r.src="//cdn.mxpnl.com/libs/mixpanel-2.2.min.js";f=t.getElementsByTagName("script")[0];f.parentNode.insertBefore(r,f)}}(document,n.mixpanel||[]),n.mixpanel.init("c9593bd4d361b0b15d95bc5410ab4439")),!0):(n.mixpanel=null,!1)}n.RDVS!=null&&RDVS.UserMonitoring==t&&(RDVS.UserMonitoring={identify:function(n,r){try{if(!i())return;if(!mixpanel||!RDVS.enableUserTracking)return;n.attributes!=t&&(n=n.attributes);r=$.extend({async:!0},r);r.async?setTimeout(function(){mixpanel.identify(n.email)},100):mixpanel.identify(n.email)}catch(u){Diag.log.error(u)}},login:function(n,t){try{if(!i())return;if(!mixpanel||!RDVS.enableUserTracking)return;t=$.extend({async:!0,callingSource:""},t);function r(){mixpanel.identify(n);var i=(t.callingSource||"")!=""?t.callingSource+" : Login":"Login";Diag.trackUser(i,n);mixpanel.track(i);mixpanel.people.set({$email:n,$last_login:new Date})}t.async?setTimeout(function(){r()},100):r()}catch(u){Diag.log.error(u)}},trackAction:function(r,u){try{if(!i())return;if(!mixpanel||!RDVS.enableUserTracking)return;u=$.extend({async:!0,callingSource:"",actor:null,actionDetails:{}},u);function f(){var i,f;n.getSessionUser!=t&&(i=getSessionUser(),i!=null&&mixpanel.identify(i.get("email")));f=(u.callingSource||"")!=""?u.callingSource+" : "+r:r;Diag.trackUser(f,u.actor!=null?u.actor.email:"");mixpanel.track(f,u.actionDetails)}u.async?setTimeout(function(){f()},100):f()}catch(e){Diag.log.error(e)}},updateUser:function(n,r){try{if(!i())return;if(!mixpanel||!RDVS.enableUserTracking)return;r=$.extend({async:!0},r);n.attributes!=t&&(n=n.attributes);function u(){mixpanel.identify(n.email);mixpanel.people.set({$email:n.email,"First Name":n.firstName,"Last Name":n.lastName,Language:n.culture,"Price Plan":n.pricePlanSubscriptionId,"Subscription Start Date":(n.subscriptionStartDate||"")!=""?Date.parse(n.subscriptionStartDate):null,"Subscription End Date":(n.subscriptionEndDate||"")!=""?Date.parse(n.subscriptionEndDate):null,"Trial End Date":(n.trialEndDate||"")!=""?Date.parse(n.trialEndDate):null})}r.async?setTimeout(function(){u()},100):u()}catch(f){Diag.log.error(f)}},createUser:function(n,r){try{if(!i())return;if(!mixpanel||!RDVS.enableUserTracking)return;n.attributes!=t&&(n=n.attributes);r=$.extend({async:!0},r);function u(){mixpanel.identify(n.email);mixpanel.people.set({$email:n.email,$created:new Date,$last_login:new Date,"First Name":n.firstName,"Last Name":n.lastName,"Price Plan":n.pricePlanSubscriptionId,Profession:n.professionName,"Promo Code":n.invitationCode,Language:n.culture,"Trial End Date":(n.trialEndDate||"")!=""?Date.parse(n.trialEndDate):null})}r.async?setTimeout(function(){u()},100):u()}catch(f){Diag.log.error(f)}}})})(window);
function Clock(n){this.handle=null;this.container=n;this.refresh=function(){var n,t,i=typeof getSessionUser=="function"?getSessionUser():null;i===null?(n=new Date,t=n.toString("H:mm")):(n=i.getNow(),t=i.timeToString(n));this.container.html(RDVSUIHelper.translateDay(n.getDay())+" "+RDVSUIHelper.formatWithPadding(n.getDate(),2)+" "+RDVSUIHelper.getMonthName(n.getMonth(),!0,!0)+", "+t)};this.show=function(){var n=this;n.refresh();$(".h-NoLink").click(function(n){n.preventDefault()});this.handle=setInterval(function(){n.refresh()},5e3)};this.hide=function(){clearInterval(this.handle)}}RDVS.pageHeader={clock:null,clockDiv:null,initialized:!1,init:function(){var n=this,t,r,u;if(!this.initialized){this.initialized=!0;this.element=$(".h-PageTopHeader");this.element.find(".h-ShowHelpCenterButton").click(function(n){n.preventDefault();var t=getSessionUser();t!=null&&HS.beacon.identify({name:t.get("fullName"),email:t.get("email")});HS.beacon.open()});t=sessionStorage.getItem("sawNotifications");r=sessionStorage.getItem("Username");t=typeof t=="undefined"||t==null?[]:JSON.parse(t);var f=function(n,t){Array.isArray(n.saw)||(n.saw=[]);n.saw.push(t)},i=_.find(t,function(n){return n.username===r}),e=function(n){return typeof n!="undefined"?n.saw:[]};model.getCronsultationReasons(function(n){var u=$(".h-notifs"),o=e(i);_.forEach(n,function(n){if(!_.contains(o,n.uid)){var t='<div class="alert">'+labels.cr_out_of_date_notification.format("<strong>"+n.title.fr+"<\/strong>")+'<i class="alert-delete fa fa-times" cr-uid="'+n.uid+'"> <\/i><\/div>';u.append($(t))}});$(".h-notifs .alert .alert-delete").click(function(n){var u=$(n.target);(typeof t=="undefined"||t==null)&&(t=[]);typeof i=="undefined"&&(i={saw:[],username:r},t.push(i));f(i,u.attr("cr-uid"));sessionStorage.setItem("sawNotifications",JSON.stringify(t));u.parent().detach();panelManager.refreshScheduleView()})});$(".dropdown-menu").not(".h-AllowClickLink").click(function(n){n.preventDefault();n.stopPropagation()});$("a.h-FollowLink").click(function(n){n.preventDefault();var t=$(this).attr("href");$(this).hasClass("h-LanguageSwitcher")&&(t=t+window.top.location.search);window.top.location=t});$(".h-WalkthroughHelp",".h-Help").click(function(n){n.preventDefault();panelManager.walkthrough!=null&&panelManager.walkthrough.toggleMenu(!0)});$(".h-OpenHelpWindow").click(function(n){var t,r,i;n.preventDefault();t=$("#HelpWindow");$(".h-Title",t).text($(this).text());r=$(this).attr("data-videourl");r!=""?$(".h-VideoFrame",t).show().attr("src",r):$(".h-VideoFrame",t).hide();i=$(this).attr("data-blogurl");i!=""?($(".h-BlogLink",t).show().text(i).attr("href",'javascript:window.open("{0}")'.format(i)),$(".h-BlogReference",t).show()):($(".h-BlogLink",t).hide(),$(".h-BlogReference",t).hide())});$("#HelpWindow").on("hidden",function(){$(".h-VideoFrame",$(this)).attr("src","")});Common.isAppOnline()?$(".h-LanguageSwitcher").show():$(".h-LanguageSwitcher").hide();RDVS.sessionInfo.isValid()?($(".h-SessionControl").show(),$(".h-NoSessionControl").hide()):($(".h-SessionControl").hide(),$(".h-NoSessionControl").show());$(".h-LogoutButton").click(function(n){if(RDVS.navigationInterceptor.hasFieldChanged){var t=labels.Label_NabigationInterceptorTitle_EditSchedule,i=labels.Content_NavigationInterceptor;panelManager.DeleteModeEnabled()&&(t=labels.Label_NabigationInterceptorTitle_EditSchedule_DeleteMode,i=labels.Content_NavigationInterceptor_DeleteMode);RDVS.navigationInterceptor.set({title:t,content:i})}RDVS.navigationInterceptor.onClickNavigationLink(function(){n.preventDefault();RDVS.sessionInfo.logout()})});$(".h-ReturnButton").click(function(){window.location="/gateway"});this.setIsVisibleReturn();$(".h-ReportIssue").click(function(){reportIssue({labels:{describeIssue:labels.ReportIssue_DescribeIssue,editScreenshot:labels.ReportIssue_EditScreenshot,sendButton:labels.Button_Send,cancelButton:labels.Button_Cancel},onBeforeScreenshot:function(){panelManager.walkthrough.helpButton.hide()},onComplete:function(n){panelManager.walkthrough.helpButton.show();Common.callDataApiMethod({type:"POST",path:"issues",data:n,onSuccess:function(){Common.notifyMessage(labels.Label_IssueSent,{title:"&nbsp;"})}})}})});this.clockDiv=$(".h-Clock");this.clockDiv.length>0&&(this.clock=new Clock($(".h-ClockValue")),this.clock.show());u=typeof getSessionUser=="function"?getSessionUser():null;u!=null&&u.getCompanies(function(t){if(t.data.companies){if(t.data.companies.length==1)n.element.find("#UserLoggedCompanySelect").hide(),n.element.find("#UserLoggedCompanyText").show(),n.element.find(".h-UserLoggedCompanyText").val(t.data.companies[0].name),n.element.find(".h-ButtonShow").hide();else{var i=n.element.find(".h-UserLoggedCompanySelect").empty();n.element.find("#UserLoggedCompanySelect").show();n.element.find("#UserLoggedCompanyText").hide();n.element.find(".h-ButtonShow").show();t.data.companies.forEach(function(n){var t=$("<option>").val(n.id).text(n.name);i.append(t)})}n.selectCompanyInHeader(RDVS.sessionInfo.getCompany())}});n.element.find(".h-ButtonShow").click(function(){RDVS.navigationInterceptor.onClickNavigationLink(function(){var t=n.element.find(".h-UserLoggedCompanySelect").val();getSessionUser().switchCompanyFor(t,function(){goSessionStore.clearSwitchCompagnie();location.reload()})})})}},initSession:function(){var n=this,t;this.element.find(".h-EnterKeyDownHoneypot").click(function(n){n.preventDefault()});$(".h-SaveButton",n.clockDiv).click(function(t){t.preventDefault();var i=getSessionUser(),r=i.getTimeFormat()!=$(".h-TimeFormat",n.clockDiv).val();i.set("timeFormat",$(".h-TimeFormat",n.clockDiv).val());i.set("timeZone",$(".h-TimeZoneDropDown",n.clockDiv).val());i.save(function(){r?window.location=window.location:($(".dropdown-toggle",n.clockDiv).click(),n.clock!=null&&n.clock.refresh(),isNotNull(panelManager)&&panelManager.synchronizeData())})});$(".h-CancelButton",n.clockDiv).click(function(t){t.preventDefault();$(".dropdown-toggle",n.clockDiv).click();n.clock!=null&&n.clock.refresh();var i=getSessionUser().get("timeZone");$(".h-TimeZoneDropDown option",n.clockDiv).each(function(){if($(this).val()==i){$(this).attr("selected","selected");return}});Common.displayNotification(labels.Message_ModificationsCancelled,"info")});$(".h-SubscribeButton").on("click",function(n){n.preventDefault();window.top.location=RDVS.pageInfo.rootUrl+"pricing/"});n.clock!=null&&n.clock.refresh();t=getSessionUser();t.get("userType")>1&&$(".h-SysAdminButton").html("<a href='/SysAdmin' class='link btn' id='SysAdminButton' visible='false'>Admin<\/a>")},selectCompanyInHeader:function(n){n!=null&&($(".h-BusinessName").text(n.get("name")),$(".h-PageTopHeader").find(".h-UserLoggedCompanySelect").find('option[value="{0}"]'.format(n.get("id"))).attr("selected","selected"))},refresh:function(){var t=RDVS.sessionInfo.getCompany(),n=getSessionUser(),r=n.get("dateFormat"),i=n.getTimeFormat();$(".h-TimeFormat",this.clockDiv).val(i);$(".h-HelloLinkLabel").html(labels.LinkButton_Welcome.format(n.escape("firstName")+", "+t.get("name")));$(".h-ProfileFullName").text(n.get("fullName"));this.selectCompanyInHeader(t);n.get("hasProfessionalAccount")?this.element.find(".h-ProfessionalHelp").show():this.element.find(".h-ProfessionalHelp").hide()},setIsVisibleReturn:function(){RDVS.sessionInfo.isGatewayAvailable()?$(".h-ReturnButton").show():$(".h-ReturnButton").hide()}};
(function(n,t){t(document).ready(function(){t("input[data-mask]").each(function(){var n=t(this),i={translation:{"#":{pattern:/\d/,optional:!0}}},r=n.val();n.val("");n.attr("data-majus")==="true"&&(i.onKeyPress=function(n,t){t.currentTarget.value=n.toUpperCase()});n.unmask();n.mask(n.attr("data-mask"),i);n.val(r)})})})(window,$),function(n){typeof define=="function"&&define.amd?define(["jquery"],n):typeof exports=="object"?module.exports=n(require("jquery")):n(jQuery||Zepto)}(function(n){var r=function(t,i,r){var f,e,o,u;t=n(t);f=this;e=t.val();i=typeof i=="function"?i(t.val(),undefined,t,r):i;u={invalid:[],getCaret:function(){try{var n,i=0,f=t.get(0),u=document.selection,r=f.selectionStart;return u&&navigator.appVersion.indexOf("MSIE 10")===-1?(n=u.createRange(),n.moveStart("character",t.is("input")?-t.val().length:-t.text().length),i=n.text.length):(r||r==="0")&&(i=r),i}catch(e){}},setCaret:function(n){try{if(t.is(":focus")){var i,r=t.get(0);r.setSelectionRange?r.setSelectionRange(n,n):r.createTextRange&&(i=r.createTextRange(),i.collapse(!0),i.moveEnd("character",n),i.moveStart("character",n),i.select())}}catch(u){}},events:function(){t.on("keyup.mask",u.behaviour).on("paste.mask drop.mask",function(){setTimeout(function(){t.keydown().keyup()},100)}).on("change.mask",function(){t.data("changed",!0)}).on("blur.mask",function(){e===t.val()||t.data("changed")||t.triggerHandler("change");t.data("changed",!1)}).on("keydown.mask, blur.mask",function(){e=t.val()}).on("focus.mask",function(t){r.selectOnFocus===!0&&n(t.target).select()}).on("focusout.mask",function(){r.clearIfNotMatch&&!o.test(u.val())&&u.val("")})},getRegexMask:function(){for(var u=[],r,e,h,s,n,o,t=0;t<i.length;t++)r=f.translation[i.charAt(t)],r?(e=r.pattern.toString().replace(/.{1}$|^.{1}/g,""),h=r.optional,s=r.recursive,s?(u.push(i.charAt(t)),n={digit:i.charAt(t),pattern:e}):u.push(!h&&!s?e:e+"?")):u.push(i.charAt(t).replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"));return o=u.join(""),n&&(o=o.replace(new RegExp("("+n.digit+"(.*"+n.digit+")?)"),"($1)?").replace(new RegExp(n.digit,"g"),n.pattern)),new RegExp(o)},destroyEvents:function(){t.off(["keydown","keyup","paste","drop","blur","focusout",""].join(".mask "))},val:function(n){var u=t.is("input"),i=u?"val":"text",r;return arguments.length>0?(t[i]()!==n&&t[i](n),r=t):r=t[i](),r},getMCharsBeforeCount:function(n,t){for(var u=0,r=0,e=i.length;r<e&&r<n;r++)f.translation[i.charAt(r)]||(n=t?n+1:n,u++);return u},caretPos:function(n,t,r,e){var o=f.translation[i.charAt(Math.min(n-1,i.length-1))];return o?Math.min(n+r-t-e,r):u.caretPos(n+1,t,r,e)},behaviour:function(t){var i;if(t=t||window.event,u.invalid=[],i=t.keyCode||t.which,n.inArray(i,f.byPassKeys)===-1){var r=u.getCaret(),h=u.val(),e=h.length,c=r<e,o=u.getMasked(),s=o.length,l=u.getMCharsBeforeCount(s-1)-u.getMCharsBeforeCount(e-1);return u.val(o),!c||i===65&&t.ctrlKey||(i===8||i===46||(r=u.caretPos(r,e,s,l)),u.setCaret(r)),u.callbacks(t)}},getMasked:function(n){var h=[],d=u.val(),t=0,l=i.length,o=0,p=d.length,e=1,a="push",v=-1,c,w,b;for(r.reverse?(a="unshift",e=-1,c=0,t=l-1,o=p-1,w=function(){return t>-1&&o>-1}):(c=l-1,w=function(){return t<l&&o<p});w();){var k=i.charAt(t),y=d.charAt(o),s=f.translation[k];s?(y.match(s.pattern)?(h[a](y),s.recursive&&(v===-1?v=t:t===c&&(t=v-e),c===v&&(t-=e)),t+=e):s.optional?(t+=e,o-=e):s.fallback?(h[a](s.fallback),t+=e,o-=e):u.invalid.push({p:o,v:y,e:s.pattern}),o+=e):(n||h[a](k),y===k&&(o+=e),t+=e)}return b=i.charAt(c),l!==p+1||f.translation[b]||h.push(b),h.join("")},callbacks:function(n){var f=u.val(),h=f!==e,s=[f,n,t,r],o=function(n,t,i){typeof r[n]=="function"&&t&&r[n].apply(this,i)};o("onChange",h===!0,s);o("onKeyPress",h===!0,s);o("onComplete",f.length===i.length,s);o("onInvalid",u.invalid.length>0,[f,n,t,u.invalid,r])}};f.mask=i;f.options=r;f.remove=function(){var n=u.getCaret();return u.destroyEvents(),u.val(f.getCleanVal()),u.setCaret(n-u.getMCharsBeforeCount(n)),t};f.getCleanVal=function(){return u.getMasked(!0)};f.init=function(i){if(i=i||!1,r=r||{},f.byPassKeys=n.jMaskGlobals.byPassKeys,f.translation=n.jMaskGlobals.translation,f.translation=n.extend({},f.translation,r.translation),f=n.extend(!0,{},f,r),o=u.getRegexMask(),i===!1){r.placeholder&&t.attr("placeholder",r.placeholder);t.attr("autocomplete","off");u.destroyEvents();u.events();var e=u.getCaret();u.val(u.getMasked());u.setCaret(e+u.getMCharsBeforeCount(e,!0))}else u.events(),u.val(u.getMasked())};f.init(!t.is("input"))},u,i,t;n.maskWatchers={};u=function(){var t=n(this),u={},f="data-mask-",e=t.attr("data-mask");return t.attr(f+"reverse")&&(u.reverse=!0),t.attr(f+"clearifnotmatch")&&(u.clearIfNotMatch=!0),t.attr(f+"selectonfocus")==="true"&&(u.selectOnFocus=!0),i(t,e,u)?t.data("mask",new r(this,e,u)):void 0};i=function(t,i,r){r=r||{};var u=n(t).data("mask"),f=JSON.stringify,e=n(t).val()||n(t).text();try{return typeof i=="function"&&(i=i(e)),typeof u!="object"||f(u.options)!==f(r)||u.mask!==i}catch(o){}};n.fn.mask=function(t,u){u=u||{};var f=this.selector,o=n.jMaskGlobals,s=n.jMaskGlobals.watchInterval,e=function(){if(i(this,t,u))return n(this).data("mask",new r(this,t,u))};return n(this).each(e),f&&f!==""&&o.watchInputs&&(clearInterval(n.maskWatchers[f]),n.maskWatchers[f]=setInterval(function(){n(document).find(f).each(e)},s)),this};n.fn.unmask=function(){return clearInterval(n.maskWatchers[this.selector]),delete n.maskWatchers[this.selector],this.each(function(){var t=n(this).data("mask")})};n.fn.cleanVal=function(){return this.data("mask").getCleanVal()};n.applyDataMask=function(t){t=t||n.jMaskGlobals.maskElements;var i=t instanceof n?t:n(t);i.filter(n.jMaskGlobals.dataMaskAttr).each(u)};n.fn.refreshDataMask=function(){n("input[data-mask]").each(function(){var t=n(this),i={translation:{"#":{pattern:/\d/,optional:!0}}},r=t.val();t.val("");t.attr("data-majus")==="true"&&(i.onKeyPress=function(n,i){i.currentTarget.value=t.toUpperCase()});t.unmask();t.mask(t.attr("data-mask"),i);t.val(r);t.keydown().keyup()})};t={maskElements:"input,td,span,div",dataMaskAttr:"*[data-mask]",dataMask:!0,watchInterval:300,watchInputs:!0,watchDataMask:!1,byPassKeys:[9,16,17,18,36,37,38,39,40,91],translation:{"0":{pattern:/\d/},"9":{pattern:/\d/,optional:!0},"#":{pattern:/\d/,recursive:!0},A:{pattern:/[a-zA-Z0-9]/,optional:!0},S:{pattern:/[a-zA-Z]/}}};n.jMaskGlobals=n.jMaskGlobals||{};t=n.jMaskGlobals=n.extend(!0,{},t,n.jMaskGlobals);t.dataMask&&n.applyDataMask();setInterval(function(){n.jMaskGlobals.watchDataMask&&n.applyDataMask()},t.watchInterval)});
//! moment.js
//! version : 2.6.0
//! authors : Tim Wood, Iskren Chernev, Moment.js contributors
//! license : MIT
//! momentjs.com
(function(n){function st(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1}}function it(n,i){function u(){t.suppressDeprecationWarnings===!1&&typeof console!="undefined"&&console.warn&&console.warn("Deprecation warning: "+n)}var r=!0;return l(function(){return r&&(u(),r=!1),i.apply(this,arguments)},i)}function li(n,t){return function(i){return r(n.call(this,i),t)}}function fu(n,t){return function(i){return this.lang().ordinal(n.call(this,i),t)}}function ai(){}function ht(n){ki(n);l(this,n)}function ct(n){var t=yi(n),i=t.year||0,r=t.quarter||0,u=t.month||0,f=t.week||0,e=t.day||0,o=t.hour||0,s=t.minute||0,h=t.second||0,c=t.millisecond||0;this._milliseconds=+c+h*1e3+s*6e4+o*36e5;this._days=+e+f*7;this._months=+u+r*3+i*12;this._data={};this._bubble()}function l(n,t){for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);return t.hasOwnProperty("toString")&&(n.toString=t.toString),t.hasOwnProperty("valueOf")&&(n.valueOf=t.valueOf),n}function eu(n){var i={};for(var t in n)n.hasOwnProperty(t)&&ti.hasOwnProperty(t)&&(i[t]=n[t]);return i}function p(n){return n<0?Math.ceil(n):Math.floor(n)}function r(n,t,i){for(var r=""+Math.abs(n),u=n>=0;r.length<t;)r="0"+r;return(u?i?"+":"":"-")+r}function lt(n,i,r,u){var o=i._milliseconds,f=i._days,e=i._months;u=u==null?!0:u;o&&n._d.setTime(+n._d+o*r);f&&fr(n,"Date",gt(n,"Date")+f*r);e&&ur(n,gt(n,"Month")+e*r);u&&t.updateOffset(n,f||e)}function at(n){return Object.prototype.toString.call(n)==="[object Array]"}function ou(n){return Object.prototype.toString.call(n)==="[object Date]"||n instanceof Date}function vi(n,t,r){for(var e=Math.min(n.length,t.length),o=Math.abs(n.length-t.length),f=0,u=0;u<e;u++)(r&&n[u]!==t[u]||!r&&i(n[u])!==i(t[u]))&&f++;return f+o}function a(n){if(n){var t=n.toLowerCase().replace(/(.)s$/,"$1");n=ru[n]||uu[t]||t}return n}function yi(n){var r={},t;for(var i in n)n.hasOwnProperty(i)&&(t=a(i),t&&(r[t]=n[i]));return r}function su(i){var r,u;if(i.indexOf("week")===0)r=7,u="day";else if(i.indexOf("month")===0)r=12,u="month";else return;t[i]=function(f,e){var o,s,c=t.fn._lang[i],h=[];if(typeof f=="number"&&(e=f,f=n),s=function(n){var i=t().utc().set(u,n);return c.call(t.fn._lang,i,f||"")},e!=null)return s(e);for(o=0;o<r;o++)h.push(s(o));return h}}function i(n){var t=+n,i=0;return t!==0&&isFinite(t)&&(i=t>=0?Math.floor(t):Math.ceil(t)),i}function vt(n,t){return new Date(Date.UTC(n,t+1,0)).getUTCDate()}function pi(n,i,r){return g(t([n,11,31+i-r]),i,r).week}function wi(n){return bi(n)?366:365}function bi(n){return n%4==0&&n%100!=0||n%400==0}function ki(n){var t;n._a&&n._pf.overflow===-2&&(t=n._a[h]<0||n._a[h]>11?h:n._a[e]<1||n._a[e]>vt(n._a[f],n._a[h])?e:n._a[o]<0||n._a[o]>23?o:n._a[b]<0||n._a[b]>59?b:n._a[k]<0||n._a[k]>59?k:n._a[d]<0||n._a[d]>999?d:-1,n._pf._overflowDayOfYear&&(t<f||t>e)&&(t=e),n._pf.overflow=t)}function di(n){return n._isValid==null&&(n._isValid=!isNaN(n._d.getTime())&&n._pf.overflow<0&&!n._pf.empty&&!n._pf.invalidMonth&&!n._pf.nullInput&&!n._pf.invalidFormat&&!n._pf.userInvalidated,n._strict&&(n._isValid=n._isValid&&n._pf.charsLeftOver===0&&n._pf.unusedTokens.length===0)),n._isValid}function yt(n){return n?n.toLowerCase().replace("_","-"):n}function pt(n,i){return i._isUTC?t(n).zone(i._offset||0):t(n).local()}function hu(n,t){return t.abbr=n,y[n]||(y[n]=new ai),y[n].set(t),y[n]}function cu(n){delete y[n]}function s(n){var f=0,r,u,i,e,o=function(n){if(!y[n]&&ii)try{require("./lang/"+n)}catch(t){}return y[n]};if(!n)return t.fn._lang;if(!at(n)){if(u=o(n),u)return u;n=[n]}while(f<n.length){for(e=yt(n[f]).split("-"),r=e.length,i=yt(n[f+1]),i=i?i.split("-"):null;r>0;){if(u=o(e.slice(0,r).join("-")),u)return u;if(i&&i.length>=r&&vi(e,i,!0)>=r-1)break;r--}f++}return t.fn._lang}function lu(n){return n.match(/\[[\s\S]/)?n.replace(/^\[|\]$/g,""):n.replace(/\\/g,"")}function au(n){for(var i=n.match(ri),t=0,r=i.length;t<r;t++)i[t]=c[i[t]]?c[i[t]]:lu(i[t]);return function(u){var f="";for(t=0;t<r;t++)f+=i[t]instanceof Function?i[t].call(u,n):i[t];return f}}function wt(n,t){return n.isValid()?(t=gi(t,n.lang()),ot[t]||(ot[t]=au(t)),ot[t](n)):n.lang().invalidDate()}function gi(n,t){function r(n){return t.longDateFormat(n)||n}var i=5;for(tt.lastIndex=0;i>=0&&tt.test(n);)n=n.replace(tt,r),tt.lastIndex=0,i-=1;return n}function vu(n,t){var i=t._strict;switch(n){case"Q":return fi;case"DDDD":return oi;case"YYYY":case"GGGG":case"gggg":return i?dr:ar;case"Y":case"G":case"g":return nu;case"YYYYYY":case"YYYYY":case"GGGGG":case"ggggg":return i?gr:vr;case"S":if(i)return fi;case"SS":if(i)return ei;case"SSS":if(i)return oi;case"DDD":return lr;case"MMM":case"MMMM":case"dd":case"ddd":case"dddd":return pr;case"a":case"A":return s(t._l)._meridiemParse;case"X":return br;case"Z":case"ZZ":return rt;case"T":return wr;case"SSSS":return yr;case"MM":case"DD":case"YY":case"GG":case"gg":case"HH":case"hh":case"mm":case"ss":case"ww":case"WW":return i?ei:ui;case"M":case"D":case"d":case"H":case"h":case"m":case"s":case"w":case"W":case"e":case"E":return ui;case"Do":return kr;default:return new RegExp(ku(bu(n.replace("\\","")),"i"))}}function nr(n){n=n||"";var r=n.match(rt)||[],f=r[r.length-1]||[],t=(f+"").match(iu)||["-",0,0],u=+(t[1]*60)+i(t[2]);return t[0]==="+"?-u:u}function yu(n,r,u){var l,c=u._a;switch(n){case"Q":r!=null&&(c[h]=(i(r)-1)*3);break;case"M":case"MM":r!=null&&(c[h]=i(r)-1);break;case"MMM":case"MMMM":l=s(u._l).monthsParse(r);l!=null?c[h]=l:u._pf.invalidMonth=r;break;case"D":case"DD":r!=null&&(c[e]=i(r));break;case"Do":r!=null&&(c[e]=i(parseInt(r,10)));break;case"DDD":case"DDDD":r!=null&&(u._dayOfYear=i(r));break;case"YY":c[f]=t.parseTwoDigitYear(r);break;case"YYYY":case"YYYYY":case"YYYYYY":c[f]=i(r);break;case"a":case"A":u._isPm=s(u._l).isPM(r);break;case"H":case"HH":case"h":case"hh":c[o]=i(r);break;case"m":case"mm":c[b]=i(r);break;case"s":case"ss":c[k]=i(r);break;case"S":case"SS":case"SSS":case"SSSS":c[d]=i(("0."+r)*1e3);break;case"X":u._d=new Date(parseFloat(r)*1e3);break;case"Z":case"ZZ":u._useUTC=!0;u._tzm=nr(r);break;case"w":case"ww":case"W":case"WW":case"d":case"dd":case"ddd":case"dddd":case"e":case"E":n=n.substr(0,1);case"gg":case"gggg":case"GG":case"GGGG":case"GGGGG":n=n.substr(0,2);r&&(u._w=u._w||{},u._w[n]=r)}}function bt(n){var r,v,l=[],y,p,w,u,a,c,k,d;if(!n._d){for(y=wu(n),n._w&&n._a[e]==null&&n._a[h]==null&&(w=function(i){var r=parseInt(i,10);return i?i.length<3?r>68?1900+r:2e3+r:r:n._a[f]==null?t().weekYear():n._a[f]},u=n._w,u.GG!=null||u.W!=null||u.E!=null?a=ir(w(u.GG),u.W||1,u.E,4,1):(c=s(n._l),k=u.d!=null?tr(u.d,c):u.e!=null?parseInt(u.e,10)+c._week.dow:0,d=parseInt(u.w,10)||1,u.d!=null&&k<c._week.dow&&d++,a=ir(w(u.gg),d,k,c._week.doy,c._week.dow)),n._a[f]=a.year,n._dayOfYear=a.dayOfYear),n._dayOfYear&&(p=n._a[f]==null?y[f]:n._a[f],n._dayOfYear>wi(p)&&(n._pf._overflowDayOfYear=!0),v=dt(p,0,n._dayOfYear),n._a[h]=v.getUTCMonth(),n._a[e]=v.getUTCDate()),r=0;r<3&&n._a[r]==null;++r)n._a[r]=l[r]=y[r];for(;r<7;r++)n._a[r]=l[r]=n._a[r]==null?r===2?1:0:n._a[r];l[o]+=i((n._tzm||0)/60);l[b]+=i((n._tzm||0)%60);n._d=(n._useUTC?dt:tf).apply(null,l)}}function pu(n){var t;n._d||(t=yi(n._i),n._a=[t.year,t.month,t.day,t.hour,t.minute,t.second,t.millisecond],bt(n))}function wu(n){var t=new Date;return n._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function kt(n){n._a=[];n._pf.empty=!0;for(var l=s(n._l),t=""+n._i,i,r,e,a=t.length,h=0,f=gi(n._f,l).match(ri)||[],u=0;u<f.length;u++)r=f[u],i=(t.match(vu(r,n))||[])[0],i&&(e=t.substr(0,t.indexOf(i)),e.length>0&&n._pf.unusedInput.push(e),t=t.slice(t.indexOf(i)+i.length),h+=i.length),c[r]?(i?n._pf.empty=!1:n._pf.unusedTokens.push(r),yu(r,i,n)):n._strict&&!i&&n._pf.unusedTokens.push(r);n._pf.charsLeftOver=a-h;t.length>0&&n._pf.unusedInput.push(t);n._isPm&&n._a[o]<12&&(n._a[o]+=12);n._isPm===!1&&n._a[o]===12&&(n._a[o]=0);bt(n);ki(n)}function bu(n){return n.replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(n,t,i,r,u){return t||i||r||u})}function ku(n){return n.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function du(n){var t,f,u,r,i;if(n._f.length===0){n._pf.invalidFormat=!0;n._d=new Date(NaN);return}for(r=0;r<n._f.length;r++)(i=0,t=l({},n),t._pf=st(),t._f=n._f[r],kt(t),di(t))&&(i+=t._pf.charsLeftOver,i+=t._pf.unusedTokens.length*10,t._pf.score=i,(u==null||i<u)&&(u=i,f=t));l(n,f||t)}function gu(n){var i,r,u=n._i,f=tu.exec(u);if(f){for(n._pf.iso=!0,i=0,r=ut.length;i<r;i++)if(ut[i][1].exec(u)){n._f=ut[i][0]+(f[6]||" ");break}for(i=0,r=ft.length;i<r;i++)if(ft[i][1].exec(u)){n._f+=ft[i][0];break}u.match(rt)&&(n._f+="Z");kt(n)}else t.createFromInputFallback(n)}function nf(i){var r=i._i,u=sr.exec(r);r===n?i._d=new Date:u?i._d=new Date(+u[1]):typeof r=="string"?gu(i):at(r)?(i._a=r.slice(0),bt(i)):ou(r)?i._d=new Date(+r):typeof r=="object"?pu(i):typeof r=="number"?i._d=new Date(r):t.createFromInputFallback(i)}function tf(n,t,i,r,u,f,e){var o=new Date(n,t,i,r,u,f,e);return n<1970&&o.setFullYear(n),o}function dt(n){var t=new Date(Date.UTC.apply(null,arguments));return n<1970&&t.setUTCFullYear(n),t}function tr(n,t){if(typeof n=="string")if(isNaN(n)){if(n=t.weekdaysParse(n),typeof n!="number")return null}else n=parseInt(n,10);return n}function rf(n,t,i,r,u){return u.relativeTime(t||1,!!i,n,r)}function uf(n,t,i){var o=w(Math.abs(n)/1e3),u=w(o/60),f=w(u/60),r=w(f/24),s=w(r/365),e=o<45&&["s",o]||u===1&&["m"]||u<45&&["mm",u]||f===1&&["h"]||f<22&&["hh",f]||r===1&&["d"]||r<=25&&["dd",r]||r<=45&&["M"]||r<345&&["MM",w(r/30)]||s===1&&["y"]||["yy",s];return e[2]=t,e[3]=n>0,e[4]=i,rf.apply({},e)}function g(n,i,r){var e=r-i,u=r-n.day(),f;return u>e&&(u-=7),u<e-7&&(u+=7),f=t(n).add("d",u),{week:Math.ceil(f.dayOfYear()/7),year:f.year()}}function ir(n,t,i,r,u){var e=dt(n,0,1).getUTCDay(),o,f;return i=i!=null?i:u,o=u-e+(e>r?7:0)-(e<u?7:0),f=7*(t-1)+(i-u)+o+1,{year:f>0?n:n-1,dayOfYear:f>0?f:wi(n-1)+f}}function rr(i){var r=i._i,u=i._f;return r===null||u===n&&r===""?t.invalid({nullInput:!0}):(typeof r=="string"&&(i._i=r=s().preparse(r)),t.isMoment(r)?(i=eu(r),i._d=new Date(+r._d)):u?at(u)?du(i):kt(i):nf(i),new ht(i))}function ur(n,t){var i;return typeof t=="string"&&(t=n.lang().monthsParse(t),typeof t!="number")?n:(i=Math.min(n.date(),vt(n.year(),t)),n._d["set"+(n._isUTC?"UTC":"")+"Month"](t,i),n)}function gt(n,t){return n._d["get"+(n._isUTC?"UTC":"")+t]()}function fr(n,t,i){return t==="Month"?ur(n,i):n._d["set"+(n._isUTC?"UTC":"")+t](i)}function v(n,i){return function(r){return r!=null?(fr(this,n,r),t.updateOffset(this,i),this):gt(this,n)}}function ff(n){t.duration.fn[n]=function(){return this._data[n]}}function er(n,i){t.duration.fn["as"+n]=function(){return+this/i}}function or(n){typeof ender=="undefined"&&(ni=nt.moment,nt.moment=n?it("Accessing Moment through the global scope is deprecated, and will be removed in an upcoming release.",t):t)}for(var t,nt=typeof global!="undefined"?global:this,ni,w=Math.round,u,f=0,h=1,e=2,o=3,b=4,k=5,d=6,y={},ti={_isAMomentObject:null,_i:null,_f:null,_l:null,_strict:null,_isUTC:null,_offset:null,_pf:null,_lang:null},ii=typeof module!="undefined"&&module.exports,sr=/^\/?Date\((\-?\d+)/i,hr=/(\-)?(?:(\d*)\.)?(\d+)\:(\d+)(?:\:(\d+)\.?(\d{3})?)?/,cr=/^(-)?P(?:(?:([0-9,.]*)Y)?(?:([0-9,.]*)M)?(?:([0-9,.]*)D)?(?:T(?:([0-9,.]*)H)?(?:([0-9,.]*)M)?(?:([0-9,.]*)S)?)?|([0-9,.]*)W)$/,ri=/(\[[^\[]*\])|(\\)?(Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Q|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|mm?|ss?|S{1,4}|X|zz?|ZZ?|.)/g,tt=/(\[[^\[]*\])|(\\)?(LT|LL?L?L?|l{1,4})/g,ui=/\d\d?/,lr=/\d{1,3}/,ar=/\d{1,4}/,vr=/[+\-]?\d{1,6}/,yr=/\d+/,pr=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,rt=/Z|[\+\-]\d\d:?\d\d/gi,wr=/T/i,br=/[\+\-]?\d+(\.\d{1,3})?/,kr=/\d{1,2}/,fi=/\d/,ei=/\d\d/,oi=/\d{3}/,dr=/\d{4}/,gr=/[+-]?\d{6}/,nu=/[+-]?\d+/,tu=/^\s*(?:[+-]\d{6}|\d{4})-(?:(\d\d-\d\d)|(W\d\d$)|(W\d\d-\d)|(\d\d\d))((T| )(\d\d(:\d\d(:\d\d(\.\d+)?)?)?)?([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ut=[["YYYYYY-MM-DD",/[+-]\d{6}-\d{2}-\d{2}/],["YYYY-MM-DD",/\d{4}-\d{2}-\d{2}/],["GGGG-[W]WW-E",/\d{4}-W\d{2}-\d/],["GGGG-[W]WW",/\d{4}-W\d{2}/],["YYYY-DDD",/\d{4}-\d{3}/]],ft=[["HH:mm:ss.SSSS",/(T| )\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss",/(T| )\d\d:\d\d:\d\d/],["HH:mm",/(T| )\d\d:\d\d/],["HH",/(T| )\d\d/]],iu=/([\+\-]|\d\d)/gi,ef="Date|Hours|Minutes|Seconds|Milliseconds".split("|"),et={Milliseconds:1,Seconds:1e3,Minutes:6e4,Hours:36e5,Days:864e5,Months:2592e6,Years:31536e6},ru={ms:"millisecond",s:"second",m:"minute",h:"hour",d:"day",D:"date",w:"week",W:"isoWeek",M:"month",Q:"quarter",y:"year",DDD:"dayOfYear",e:"weekday",E:"isoWeekday",gg:"weekYear",GG:"isoWeekYear"},uu={dayofyear:"dayOfYear",isoweekday:"isoWeekday",isoweek:"isoWeek",weekyear:"weekYear",isoweekyear:"isoWeekYear"},ot={},si="DDD w W M D d".split(" "),hi="M D H h m s w W".split(" "),c={M:function(){return this.month()+1},MMM:function(n){return this.lang().monthsShort(this,n)},MMMM:function(n){return this.lang().months(this,n)},D:function(){return this.date()},DDD:function(){return this.dayOfYear()},d:function(){return this.day()},dd:function(n){return this.lang().weekdaysMin(this,n)},ddd:function(n){return this.lang().weekdaysShort(this,n)},dddd:function(n){return this.lang().weekdays(this,n)},w:function(){return this.week()},W:function(){return this.isoWeek()},YY:function(){return r(this.year()%100,2)},YYYY:function(){return r(this.year(),4)},YYYYY:function(){return r(this.year(),5)},YYYYYY:function(){var n=this.year(),t=n>=0?"+":"-";return t+r(Math.abs(n),6)},gg:function(){return r(this.weekYear()%100,2)},gggg:function(){return r(this.weekYear(),4)},ggggg:function(){return r(this.weekYear(),5)},GG:function(){return r(this.isoWeekYear()%100,2)},GGGG:function(){return r(this.isoWeekYear(),4)},GGGGG:function(){return r(this.isoWeekYear(),5)},e:function(){return this.weekday()},E:function(){return this.isoWeekday()},a:function(){return this.lang().meridiem(this.hours(),this.minutes(),!0)},A:function(){return this.lang().meridiem(this.hours(),this.minutes(),!1)},H:function(){return this.hours()},h:function(){return this.hours()%12||12},m:function(){return this.minutes()},s:function(){return this.seconds()},S:function(){return i(this.milliseconds()/100)},SS:function(){return r(i(this.milliseconds()/10),2)},SSS:function(){return r(this.milliseconds(),3)},SSSS:function(){return r(this.milliseconds(),3)},Z:function(){var n=-this.zone(),t="+";return n<0&&(n=-n,t="-"),t+r(i(n/60),2)+":"+r(i(n)%60,2)},ZZ:function(){var n=-this.zone(),t="+";return n<0&&(n=-n,t="-"),t+r(i(n/60),2)+r(i(n)%60,2)},z:function(){return this.zoneAbbr()},zz:function(){return this.zoneName()},X:function(){return this.unix()},Q:function(){return this.quarter()}},ci=["months","monthsShort","weekdays","weekdaysShort","weekdaysMin"];si.length;)u=si.pop(),c[u+"o"]=fu(c[u],u);while(hi.length)u=hi.pop(),c[u+u]=li(c[u],2);for(c.DDDD=li(c.DDD,3),l(ai.prototype,{set:function(n){var t;for(var i in n)t=n[i],typeof t=="function"?this[i]=t:this["_"+i]=t},_months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),months:function(n){return this._months[n.month()]},_monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),monthsShort:function(n){return this._monthsShort[n.month()]},monthsParse:function(n){var i,r,u;for(this._monthsParse||(this._monthsParse=[]),i=0;i<12;i++)if(this._monthsParse[i]||(r=t.utc([2e3,i]),u="^"+this.months(r,"")+"|^"+this.monthsShort(r,""),this._monthsParse[i]=new RegExp(u.replace(".",""),"i")),this._monthsParse[i].test(n))return i},_weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdays:function(n){return this._weekdays[n.day()]},_weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysShort:function(n){return this._weekdaysShort[n.day()]},_weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),weekdaysMin:function(n){return this._weekdaysMin[n.day()]},weekdaysParse:function(n){var i,r,u;for(this._weekdaysParse||(this._weekdaysParse=[]),i=0;i<7;i++)if(this._weekdaysParse[i]||(r=t([2e3,1]).day(i),u="^"+this.weekdays(r,"")+"|^"+this.weekdaysShort(r,"")+"|^"+this.weekdaysMin(r,""),this._weekdaysParse[i]=new RegExp(u.replace(".",""),"i")),this._weekdaysParse[i].test(n))return i},_longDateFormat:{LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D YYYY",LLL:"MMMM D YYYY LT",LLLL:"dddd, MMMM D YYYY LT"},longDateFormat:function(n){var t=this._longDateFormat[n];return!t&&this._longDateFormat[n.toUpperCase()]&&(t=this._longDateFormat[n.toUpperCase()].replace(/MMMM|MM|DD|dddd/g,function(n){return n.slice(1)}),this._longDateFormat[n]=t),t},isPM:function(n){return(n+"").toLowerCase().charAt(0)==="p"},_meridiemParse:/[ap]\.?m?\.?/i,meridiem:function(n,t,i){return n>11?i?"pm":"PM":i?"am":"AM"},_calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},calendar:function(n,t){var i=this._calendar[n];return typeof i=="function"?i.apply(t):i},_relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},relativeTime:function(n,t,i,r){var u=this._relativeTime[i];return typeof u=="function"?u(n,t,i,r):u.replace(/%d/i,n)},pastFuture:function(n,t){var i=this._relativeTime[n>0?"future":"past"];return typeof i=="function"?i(t):i.replace(/%s/i,t)},ordinal:function(n){return this._ordinal.replace("%d",n)},_ordinal:"%d",preparse:function(n){return n},postformat:function(n){return n},week:function(n){return g(n,this._week.dow,this._week.doy).week},_week:{dow:0,doy:6},_invalidDate:"Invalid date",invalidDate:function(){return this._invalidDate}}),t=function(t,i,r,u){var f;return typeof r=="boolean"&&(u=r,r=n),f={},f._isAMomentObject=!0,f._i=t,f._f=i,f._l=r,f._strict=u,f._isUTC=!1,f._pf=st(),rr(f)},t.suppressDeprecationWarnings=!1,t.createFromInputFallback=it("moment construction falls back to js Date. This is discouraged and will be removed in upcoming major release. Please refer to https://github.com/moment/moment/issues/1407 for more info.",function(n){n._d=new Date(n._i)}),t.utc=function(t,i,r,u){var f;return typeof r=="boolean"&&(u=r,r=n),f={},f._isAMomentObject=!0,f._useUTC=!0,f._isUTC=!0,f._l=r,f._i=t,f._f=i,f._strict=u,f._pf=st(),rr(f).utc()},t.unix=function(n){return t(n*1e3)},t.duration=function(n,r){var h=n,u=null,f,c,s;return t.isDuration(n)?h={ms:n._milliseconds,d:n._days,M:n._months}:typeof n=="number"?(h={},r?h[r]=n:h.milliseconds=n):(u=hr.exec(n))?(f=u[1]==="-"?-1:1,h={y:0,d:i(u[e])*f,h:i(u[o])*f,m:i(u[b])*f,s:i(u[k])*f,ms:i(u[d])*f}):!(u=cr.exec(n))||(f=u[1]==="-"?-1:1,s=function(n){var t=n&&parseFloat(n.replace(",","."));return(isNaN(t)?0:t)*f},h={y:s(u[2]),M:s(u[3]),d:s(u[4]),h:s(u[5]),m:s(u[6]),s:s(u[7]),w:s(u[8])}),c=new ct(h),t.isDuration(n)&&n.hasOwnProperty("_lang")&&(c._lang=n._lang),c},t.version="2.6.0",t.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",t.momentProperties=ti,t.updateOffset=function(){},t.lang=function(n,i){var r;return n?(i?hu(yt(n),i):i===null?(cu(n),n="en"):y[n]||s(n),r=t.duration.fn._lang=t.fn._lang=s(n),r._abbr):t.fn._lang._abbr},t.langData=function(n){return n&&n._lang&&n._lang._abbr&&(n=n._lang._abbr),s(n)},t.isMoment=function(n){return n instanceof ht||n!=null&&n.hasOwnProperty("_isAMomentObject")},t.isDuration=function(n){return n instanceof ct},u=ci.length-1;u>=0;--u)su(ci[u]);t.normalizeUnits=function(n){return a(n)};t.invalid=function(n){var i=t.utc(NaN);return n!=null?l(i._pf,n):i._pf.userInvalidated=!0,i};t.parseZone=function(){return t.apply(null,arguments).parseZone()};t.parseTwoDigitYear=function(n){return i(n)+(i(n)>68?1900:2e3)};l(t.fn=ht.prototype,{clone:function(){return t(this)},valueOf:function(){return+this._d+(this._offset||0)*6e4},unix:function(){return Math.floor(+this/1e3)},toString:function(){return this.clone().lang("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},toDate:function(){return this._offset?new Date(+this):this._d},toISOString:function(){var n=t(this).utc();return 0<n.year()&&n.year()<=9999?wt(n,"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]"):wt(n,"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]")},toArray:function(){var n=this;return[n.year(),n.month(),n.date(),n.hours(),n.minutes(),n.seconds(),n.milliseconds()]},isValid:function(){return di(this)},isDSTShifted:function(){return this._a?this.isValid()&&vi(this._a,(this._isUTC?t.utc(this._a):t(this._a)).toArray())>0:!1},parsingFlags:function(){return l({},this._pf)},invalidAt:function(){return this._pf.overflow},utc:function(){return this.zone(0)},local:function(){return this.zone(0),this._isUTC=!1,this},format:function(n){var i=wt(this,n||t.defaultFormat);return this.lang().postformat(i)},add:function(n,i){var r;return r=typeof n=="string"?t.duration(+i,n):t.duration(n,i),lt(this,r,1),this},subtract:function(n,i){var r;return r=typeof n=="string"?t.duration(+i,n):t.duration(n,i),lt(this,r,-1),this},diff:function(n,i,r){var f=pt(n,this),o=(this.zone()-f.zone())*6e4,u,e;return i=a(i),i==="year"||i==="month"?(u=(this.daysInMonth()+f.daysInMonth())*432e5,e=(this.year()-f.year())*12+(this.month()-f.month()),e+=(this-t(this).startOf("month")-(f-t(f).startOf("month")))/u,e-=(this.zone()-t(this).startOf("month").zone()-(f.zone()-t(f).startOf("month").zone()))*6e4/u,i==="year"&&(e=e/12)):(u=this-f,e=i==="second"?u/1e3:i==="minute"?u/6e4:i==="hour"?u/36e5:i==="day"?(u-o)/864e5:i==="week"?(u-o)/6048e5:u),r?e:p(e)},from:function(n,i){return t.duration(this.diff(n)).lang(this.lang()._abbr).humanize(!i)},fromNow:function(n){return this.from(t(),n)},calendar:function(){var i=pt(t(),this).startOf("day"),n=this.diff(i,"days",!0),r=n<-6?"sameElse":n<-1?"lastWeek":n<0?"lastDay":n<1?"sameDay":n<2?"nextDay":n<7?"nextWeek":"sameElse";return this.format(this.lang().calendar(r,this))},isLeapYear:function(){return bi(this.year())},isDST:function(){return this.zone()<this.clone().month(0).zone()||this.zone()<this.clone().month(5).zone()},day:function(n){var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return n!=null?(n=tr(n,this.lang()),this.add({d:n-t})):t},month:v("Month",!0),startOf:function(n){n=a(n);switch(n){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return n==="week"?this.weekday(0):n==="isoWeek"&&this.isoWeekday(1),n==="quarter"&&this.month(Math.floor(this.month()/3)*3),this},endOf:function(n){return n=a(n),this.startOf(n).add(n==="isoWeek"?"week":n,1).subtract("ms",1)},isAfter:function(n,i){return i=typeof i!="undefined"?i:"millisecond",+this.clone().startOf(i)>+t(n).startOf(i)},isBefore:function(n,i){return i=typeof i!="undefined"?i:"millisecond",+this.clone().startOf(i)<+t(n).startOf(i)},isSame:function(n,t){return t=t||"ms",+this.clone().startOf(t)==+pt(n,this).startOf(t)},min:function(n){return n=t.apply(null,arguments),n<this?this:n},max:function(n){return n=t.apply(null,arguments),n>this?this:n},zone:function(n,i){var r=this._offset||0;if(n!=null)typeof n=="string"&&(n=nr(n)),Math.abs(n)<16&&(n=n*60),this._offset=n,this._isUTC=!0,r!==n&&(!i||this._changeInProgress?lt(this,t.duration(r-n,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,t.updateOffset(this,!0),this._changeInProgress=null));else return this._isUTC?r:this._d.getTimezoneOffset();return this},zoneAbbr:function(){return this._isUTC?"UTC":""},zoneName:function(){return this._isUTC?"Coordinated Universal Time":""},parseZone:function(){return this._tzm?this.zone(this._tzm):typeof this._i=="string"&&this.zone(this._i),this},hasAlignedHourOffset:function(n){return n=n?t(n).zone():0,(this.zone()-n)%60==0},daysInMonth:function(){return vt(this.year(),this.month())},dayOfYear:function(n){var i=w((t(this).startOf("day")-t(this).startOf("year"))/864e5)+1;return n==null?i:this.add("d",n-i)},quarter:function(n){return n==null?Math.ceil((this.month()+1)/3):this.month((n-1)*3+this.month()%3)},weekYear:function(n){var t=g(this,this.lang()._week.dow,this.lang()._week.doy).year;return n==null?t:this.add("y",n-t)},isoWeekYear:function(n){var t=g(this,1,4).year;return n==null?t:this.add("y",n-t)},week:function(n){var t=this.lang().week(this);return n==null?t:this.add("d",(n-t)*7)},isoWeek:function(n){var t=g(this,1,4).week;return n==null?t:this.add("d",(n-t)*7)},weekday:function(n){var t=(this.day()+7-this.lang()._week.dow)%7;return n==null?t:this.add("d",n-t)},isoWeekday:function(n){return n==null?this.day()||7:this.day(this.day()%7?n:n-7)},isoWeeksInYear:function(){return pi(this.year(),1,4)},weeksInYear:function(){var n=this._lang._week;return pi(this.year(),n.dow,n.doy)},get:function(n){return n=a(n),this[n]()},set:function(n,t){return n=a(n),typeof this[n]=="function"&&this[n](t),this},lang:function(t){return t===n?this._lang:(this._lang=s(t),this)}});t.fn.millisecond=t.fn.milliseconds=v("Milliseconds",!1);t.fn.second=t.fn.seconds=v("Seconds",!1);t.fn.minute=t.fn.minutes=v("Minutes",!1);t.fn.hour=t.fn.hours=v("Hours",!0);t.fn.date=v("Date",!0);t.fn.dates=it("dates accessor is deprecated. Use date instead.",v("Date",!0));t.fn.year=v("FullYear",!0);t.fn.years=it("years accessor is deprecated. Use year instead.",v("FullYear",!0));t.fn.days=t.fn.day;t.fn.months=t.fn.month;t.fn.weeks=t.fn.week;t.fn.isoWeeks=t.fn.isoWeek;t.fn.quarters=t.fn.quarter;t.fn.toJSON=t.fn.toISOString;l(t.duration.fn=ct.prototype,{_bubble:function(){var e=this._milliseconds,t=this._days,i=this._months,n=this._data,r,u,f,o;n.milliseconds=e%1e3;r=p(e/1e3);n.seconds=r%60;u=p(r/60);n.minutes=u%60;f=p(u/60);n.hours=f%24;t+=p(f/24);n.days=t%30;i+=p(t/30);n.months=i%12;o=p(i/12);n.years=o},weeks:function(){return p(this.days()/7)},valueOf:function(){return this._milliseconds+this._days*864e5+this._months%12*2592e6+i(this._months/12)*31536e6},humanize:function(n){var i=+this,t=uf(i,!n,this.lang());return n&&(t=this.lang().pastFuture(i,t)),this.lang().postformat(t)},add:function(n,i){var r=t.duration(n,i);return this._milliseconds+=r._milliseconds,this._days+=r._days,this._months+=r._months,this._bubble(),this},subtract:function(n,i){var r=t.duration(n,i);return this._milliseconds-=r._milliseconds,this._days-=r._days,this._months-=r._months,this._bubble(),this},get:function(n){return n=a(n),this[n.toLowerCase()+"s"]()},as:function(n){return n=a(n),this["as"+n.charAt(0).toUpperCase()+n.slice(1)+"s"]()},lang:t.fn.lang,toIsoString:function(){var r=Math.abs(this.years()),u=Math.abs(this.months()),f=Math.abs(this.days()),n=Math.abs(this.hours()),t=Math.abs(this.minutes()),i=Math.abs(this.seconds()+this.milliseconds()/1e3);return this.asSeconds()?(this.asSeconds()<0?"-":"")+"P"+(r?r+"Y":"")+(u?u+"M":"")+(f?f+"D":"")+(n||t||i?"T":"")+(n?n+"H":"")+(t?t+"M":"")+(i?i+"S":""):"P0D"}});for(u in et)et.hasOwnProperty(u)&&(er(u,et[u]),ff(u.toLowerCase()));er("Weeks",6048e5);t.duration.fn.asMonths=function(){return(+this-this.years()*31536e6)/2592e6+this.years()*12};t.lang("en",{ordinal:function(n){var t=n%10,r=i(n%100/10)===1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+r}}),function(n){n(t)}(function(n){return n.lang("ar-ma",{months:"ÙŠÙ†Ø§ÙŠØ±_ÙØ¨Ø±Ø§ÙŠØ±_Ù…Ø§Ø±Ø³_Ø£Ø¨Ø±ÙŠÙ„_Ù…Ø§ÙŠ_ÙŠÙˆÙ†ÙŠÙˆ_ÙŠÙˆÙ„ÙŠÙˆØ²_ØºØ´Øª_Ø´ØªÙ†Ø¨Ø±_Ø£ÙƒØªÙˆØ¨Ø±_Ù†ÙˆÙ†Ø¨Ø±_Ø¯Ø¬Ù†Ø¨Ø±".split("_"),monthsShort:"ÙŠÙ†Ø§ÙŠØ±_ÙØ¨Ø±Ø§ÙŠØ±_Ù…Ø§Ø±Ø³_Ø£Ø¨Ø±ÙŠÙ„_Ù…Ø§ÙŠ_ÙŠÙˆÙ†ÙŠÙˆ_ÙŠÙˆÙ„ÙŠÙˆØ²_ØºØ´Øª_Ø´ØªÙ†Ø¨Ø±_Ø£ÙƒØªÙˆØ¨Ø±_Ù†ÙˆÙ†Ø¨Ø±_Ø¯Ø¬Ù†Ø¨Ø±".split("_"),weekdays:"Ø§Ù„Ø£Ø­Ø¯_Ø§Ù„Ø¥ØªÙ†ÙŠÙ†_Ø§Ù„Ø«Ù„Ø§Ø«Ø§Ø¡_Ø§Ù„Ø£Ø±Ø¨Ø¹Ø§Ø¡_Ø§Ù„Ø®Ù…ÙŠØ³_Ø§Ù„Ø¬Ù…Ø¹Ø©_Ø§Ù„Ø³Ø¨Øª".split("_"),weekdaysShort:"Ø§Ø­Ø¯_Ø§ØªÙ†ÙŠÙ†_Ø«Ù„Ø§Ø«Ø§Ø¡_Ø§Ø±Ø¨Ø¹Ø§Ø¡_Ø®Ù…ÙŠØ³_Ø¬Ù…Ø¹Ø©_Ø³Ø¨Øª".split("_"),weekdaysMin:"Ø­_Ù†_Ø«_Ø±_Ø®_Ø¬_Ø³".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:"[Ø§Ù„ÙŠÙˆÙ… Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",nextDay:"[ØºØ¯Ø§ Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",nextWeek:"dddd [Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",lastDay:"[Ø£Ù…Ø³ Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",lastWeek:"dddd [Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",sameElse:"L"},relativeTime:{future:"ÙÙŠ %s",past:"Ù…Ù†Ø° %s",s:"Ø«ÙˆØ§Ù†",m:"Ø¯Ù‚ÙŠÙ‚Ø©",mm:"%d Ø¯Ù‚Ø§Ø¦Ù‚",h:"Ø³Ø§Ø¹Ø©",hh:"%d Ø³Ø§Ø¹Ø§Øª",d:"ÙŠÙˆÙ…",dd:"%d Ø£ÙŠØ§Ù…",M:"Ø´Ù‡Ø±",MM:"%d Ø£Ø´Ù‡Ø±",y:"Ø³Ù†Ø©",yy:"%d Ø³Ù†ÙˆØ§Øª"},week:{dow:6,doy:12}})}),function(n){n(t)}(function(n){return n.lang("ar",{months:"ÙŠÙ†Ø§ÙŠØ±/ ÙƒØ§Ù†ÙˆÙ† Ø§Ù„Ø«Ø§Ù†ÙŠ_ÙØ¨Ø±Ø§ÙŠØ±/ Ø´Ø¨Ø§Ø·_Ù…Ø§Ø±Ø³/ Ø¢Ø°Ø§Ø±_Ø£Ø¨Ø±ÙŠÙ„/ Ù†ÙŠØ³Ø§Ù†_Ù…Ø§ÙŠÙˆ/ Ø£ÙŠØ§Ø±_ÙŠÙˆÙ†ÙŠÙˆ/ Ø­Ø²ÙŠØ±Ø§Ù†_ÙŠÙˆÙ„ÙŠÙˆ/ ØªÙ…ÙˆØ²_Ø£ØºØ³Ø·Ø³/ Ø¢Ø¨_Ø³Ø¨ØªÙ…Ø¨Ø±/ Ø£ÙŠÙ„ÙˆÙ„_Ø£ÙƒØªÙˆØ¨Ø±/ ØªØ´Ø±ÙŠÙ† Ø§Ù„Ø£ÙˆÙ„_Ù†ÙˆÙÙ…Ø¨Ø±/ ØªØ´Ø±ÙŠÙ† Ø§Ù„Ø«Ø§Ù†ÙŠ_Ø¯ÙŠØ³Ù…Ø¨Ø±/ ÙƒØ§Ù†ÙˆÙ† Ø§Ù„Ø£ÙˆÙ„".split("_"),monthsShort:"ÙŠÙ†Ø§ÙŠØ±/ ÙƒØ§Ù†ÙˆÙ† Ø§Ù„Ø«Ø§Ù†ÙŠ_ÙØ¨Ø±Ø§ÙŠØ±/ Ø´Ø¨Ø§Ø·_Ù…Ø§Ø±Ø³/ Ø¢Ø°Ø§Ø±_Ø£Ø¨Ø±ÙŠÙ„/ Ù†ÙŠØ³Ø§Ù†_Ù…Ø§ÙŠÙˆ/ Ø£ÙŠØ§Ø±_ÙŠÙˆÙ†ÙŠÙˆ/ Ø­Ø²ÙŠØ±Ø§Ù†_ÙŠÙˆÙ„ÙŠÙˆ/ ØªÙ…ÙˆØ²_Ø£ØºØ³Ø·Ø³/ Ø¢Ø¨_Ø³Ø¨ØªÙ…Ø¨Ø±/ Ø£ÙŠÙ„ÙˆÙ„_Ø£ÙƒØªÙˆØ¨Ø±/ ØªØ´Ø±ÙŠÙ† Ø§Ù„Ø£ÙˆÙ„_Ù†ÙˆÙÙ…Ø¨Ø±/ ØªØ´Ø±ÙŠÙ† Ø§Ù„Ø«Ø§Ù†ÙŠ_Ø¯ÙŠØ³Ù…Ø¨Ø±/ ÙƒØ§Ù†ÙˆÙ† Ø§Ù„Ø£ÙˆÙ„".split("_"),weekdays:"Ø§Ù„Ø£Ø­Ø¯_Ø§Ù„Ø¥Ø«Ù†ÙŠÙ†_Ø§Ù„Ø«Ù„Ø§Ø«Ø§Ø¡_Ø§Ù„Ø£Ø±Ø¨Ø¹Ø§Ø¡_Ø§Ù„Ø®Ù…ÙŠØ³_Ø§Ù„Ø¬Ù…Ø¹Ø©_Ø§Ù„Ø³Ø¨Øª".split("_"),weekdaysShort:"Ø§Ù„Ø£Ø­Ø¯_Ø§Ù„Ø¥Ø«Ù†ÙŠÙ†_Ø§Ù„Ø«Ù„Ø§Ø«Ø§Ø¡_Ø§Ù„Ø£Ø±Ø¨Ø¹Ø§Ø¡_Ø§Ù„Ø®Ù…ÙŠØ³_Ø§Ù„Ø¬Ù…Ø¹Ø©_Ø§Ù„Ø³Ø¨Øª".split("_"),weekdaysMin:"Ø­_Ù†_Ø«_Ø±_Ø®_Ø¬_Ø³".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:"[Ø§Ù„ÙŠÙˆÙ… Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",nextDay:"[ØºØ¯Ø§ Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",nextWeek:"dddd [Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",lastDay:"[Ø£Ù…Ø³ Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",lastWeek:"dddd [Ø¹Ù„Ù‰ Ø§Ù„Ø³Ø§Ø¹Ø©] LT",sameElse:"L"},relativeTime:{future:"ÙÙŠ %s",past:"Ù…Ù†Ø° %s",s:"Ø«ÙˆØ§Ù†",m:"Ø¯Ù‚ÙŠÙ‚Ø©",mm:"%d Ø¯Ù‚Ø§Ø¦Ù‚",h:"Ø³Ø§Ø¹Ø©",hh:"%d Ø³Ø§Ø¹Ø§Øª",d:"ÙŠÙˆÙ…",dd:"%d Ø£ÙŠØ§Ù…",M:"Ø´Ù‡Ø±",MM:"%d Ø£Ø´Ù‡Ø±",y:"Ø³Ù†Ø©",yy:"%d Ø³Ù†ÙˆØ§Øª"},week:{dow:6,doy:12}})}),function(n){n(t)}(function(n){return n.lang("bg",{months:"ÑÐ½ÑƒÐ°Ñ€Ð¸_Ñ„ÐµÐ²Ñ€ÑƒÐ°Ñ€Ð¸_Ð¼Ð°Ñ€Ñ‚_Ð°Ð¿Ñ€Ð¸Ð»_Ð¼Ð°Ð¹_ÑŽÐ½Ð¸_ÑŽÐ»Ð¸_Ð°Ð²Ð³ÑƒÑÑ‚_ÑÐµÐ¿Ñ‚ÐµÐ¼Ð²Ñ€Ð¸_Ð¾ÐºÑ‚Ð¾Ð¼Ð²Ñ€Ð¸_Ð½Ð¾ÐµÐ¼Ð²Ñ€Ð¸_Ð´ÐµÐºÐµÐ¼Ð²Ñ€Ð¸".split("_"),monthsShort:"ÑÐ½Ñ€_Ñ„ÐµÐ²_Ð¼Ð°Ñ€_Ð°Ð¿Ñ€_Ð¼Ð°Ð¹_ÑŽÐ½Ð¸_ÑŽÐ»Ð¸_Ð°Ð²Ð³_ÑÐµÐ¿_Ð¾ÐºÑ‚_Ð½Ð¾Ðµ_Ð´ÐµÐº".split("_"),weekdays:"Ð½ÐµÐ´ÐµÐ»Ñ_Ð¿Ð¾Ð½ÐµÐ´ÐµÐ»Ð½Ð¸Ðº_Ð²Ñ‚Ð¾Ñ€Ð½Ð¸Ðº_ÑÑ€ÑÐ´Ð°_Ñ‡ÐµÑ‚Ð²ÑŠÑ€Ñ‚ÑŠÐº_Ð¿ÐµÑ‚ÑŠÐº_ÑÑŠÐ±Ð¾Ñ‚Ð°".split("_"),weekdaysShort:"Ð½ÐµÐ´_Ð¿Ð¾Ð½_Ð²Ñ‚Ð¾_ÑÑ€Ñ_Ñ‡ÐµÑ‚_Ð¿ÐµÑ‚_ÑÑŠÐ±".split("_"),weekdaysMin:"Ð½Ð´_Ð¿Ð½_Ð²Ñ‚_ÑÑ€_Ñ‡Ñ‚_Ð¿Ñ‚_ÑÐ±".split("_"),longDateFormat:{LT:"H:mm",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[Ð”Ð½ÐµÑ Ð²] LT",nextDay:"[Ð£Ñ‚Ñ€Ðµ Ð²] LT",nextWeek:"dddd [Ð²] LT",lastDay:"[Ð’Ñ‡ÐµÑ€Ð° Ð²] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Ð’ Ð¸Ð·Ð¼Ð¸Ð½Ð°Ð»Ð°Ñ‚Ð°] dddd [Ð²] LT";case 1:case 2:case 4:case 5:return"[Ð’ Ð¸Ð·Ð¼Ð¸Ð½Ð°Ð»Ð¸Ñ] dddd [Ð²] LT"}},sameElse:"L"},relativeTime:{future:"ÑÐ»ÐµÐ´ %s",past:"Ð¿Ñ€ÐµÐ´Ð¸ %s",s:"Ð½ÑÐºÐ¾Ð»ÐºÐ¾ ÑÐµÐºÑƒÐ½Ð´Ð¸",m:"Ð¼Ð¸Ð½ÑƒÑ‚Ð°",mm:"%d Ð¼Ð¸Ð½ÑƒÑ‚Ð¸",h:"Ñ‡Ð°Ñ",hh:"%d Ñ‡Ð°ÑÐ°",d:"Ð´ÐµÐ½",dd:"%d Ð´Ð½Ð¸",M:"Ð¼ÐµÑÐµÑ†",MM:"%d Ð¼ÐµÑÐµÑ†Ð°",y:"Ð³Ð¾Ð´Ð¸Ð½Ð°",yy:"%d Ð³Ð¾Ð´Ð¸Ð½Ð¸"},ordinal:function(n){var t=n%10,i=n%100;return n===0?n+"-ÐµÐ²":i===0?n+"-ÐµÐ½":i>10&&i<20?n+"-Ñ‚Ð¸":t===1?n+"-Ð²Ð¸":t===2?n+"-Ñ€Ð¸":t===7||t===8?n+"-Ð¼Ð¸":n+"-Ñ‚Ð¸"},week:{dow:1,doy:7}})}),function(n){n(t)}(function(t){function i(n,t,i){return n+" "+f({mm:"munutenn",MM:"miz",dd:"devezh"}[i],n)}function u(n){switch(r(n)){case 1:case 3:case 4:case 5:case 9:return n+" bloaz";default:return n+" vloaz"}}function r(n){return n>9?r(n%10):n}function f(n,t){return t===2?e(n):n}function e(t){var i={m:"v",b:"v",d:"z"};return i[t.charAt(0)]===n?t:i[t.charAt(0)]+t.substring(1)}return t.lang("br",{months:"Genver_C'hwevrer_Meurzh_Ebrel_Mae_Mezheven_Gouere_Eost_Gwengolo_Here_Du_Kerzu".split("_"),monthsShort:"Gen_C'hwe_Meu_Ebr_Mae_Eve_Gou_Eos_Gwe_Her_Du_Ker".split("_"),weekdays:"Sul_Lun_Meurzh_Merc'her_Yaou_Gwener_Sadorn".split("_"),weekdaysShort:"Sul_Lun_Meu_Mer_Yao_Gwe_Sad".split("_"),weekdaysMin:"Su_Lu_Me_Mer_Ya_Gw_Sa".split("_"),longDateFormat:{LT:"h[e]mm A",L:"DD/MM/YYYY",LL:"D [a viz] MMMM YYYY",LLL:"D [a viz] MMMM YYYY LT",LLLL:"dddd, D [a viz] MMMM YYYY LT"},calendar:{sameDay:"[Hiziv da] LT",nextDay:"[Warc'hoazh da] LT",nextWeek:"dddd [da] LT",lastDay:"[Dec'h da] LT",lastWeek:"dddd [paset da] LT",sameElse:"L"},relativeTime:{future:"a-benn %s",past:"%s 'zo",s:"un nebeud segondennoÃ¹",m:"ur vunutenn",mm:i,h:"un eur",hh:"%d eur",d:"un devezh",dd:i,M:"ur miz",MM:i,y:"ur bloaz",yy:u},ordinal:function(n){var t=n===1?"aÃ±":"vet";return n+t},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){function t(n,t,i){var r=n+" ";switch(i){case"m":return t?"jedna minuta":"jedne minute";case"mm":return r+(n===1?"minuta":n===2||n===3||n===4?"minute":"minuta");case"h":return t?"jedan sat":"jednog sata";case"hh":return r+(n===1?"sat":n===2||n===3||n===4?"sata":"sati");case"dd":return r+(n===1?"dan":"dana");case"MM":return r+(n===1?"mjesec":n===2||n===3||n===4?"mjeseca":"mjeseci");case"yy":return r+(n===1?"godina":n===2||n===3||n===4?"godine":"godina")}}return n.lang("bs",{months:"januar_februar_mart_april_maj_juni_juli_avgust_septembar_oktobar_novembar_decembar".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split("_"),weekdays:"nedjelja_ponedjeljak_utorak_srijeda_Äetvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._Äet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_Äe_pe_su".split("_"),longDateFormat:{LT:"H:mm",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd, D. MMMM YYYY LT"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juÄer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[proÅ¡lu] dddd [u] LT";case 6:return"[proÅ¡le] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[proÅ¡li] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",m:t,mm:t,h:t,hh:t,d:"dan",dd:t,M:"mjesec",MM:t,y:"godinu",yy:t},ordinal:"%d.",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("ca",{months:"gener_febrer_marÃ§_abril_maig_juny_juliol_agost_setembre_octubre_novembre_desembre".split("_"),monthsShort:"gen._febr._mar._abr._mai._jun._jul._ag._set._oct._nov._des.".split("_"),weekdays:"diumenge_dilluns_dimarts_dimecres_dijous_divendres_dissabte".split("_"),weekdaysShort:"dg._dl._dt._dc._dj._dv._ds.".split("_"),weekdaysMin:"Dg_Dl_Dt_Dc_Dj_Dv_Ds".split("_"),longDateFormat:{LT:"H:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:function(){return"[avui a "+(this.hours()!==1?"les":"la")+"] LT"},nextDay:function(){return"[demÃ  a "+(this.hours()!==1?"les":"la")+"] LT"},nextWeek:function(){return"dddd [a "+(this.hours()!==1?"les":"la")+"] LT"},lastDay:function(){return"[ahir a "+(this.hours()!==1?"les":"la")+"] LT"},lastWeek:function(){return"[el] dddd [passat a "+(this.hours()!==1?"les":"la")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"fa %s",s:"uns segons",m:"un minut",mm:"%d minuts",h:"una hora",hh:"%d hores",d:"un dia",dd:"%d dies",M:"un mes",MM:"%d mesos",y:"un any",yy:"%d anys"},ordinal:"%dÂº",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){function i(n){return n>1&&n<5&&~~(n/10)!=1}function t(n,t,r,u){var f=n+" ";switch(r){case"s":return t||u?"pÃ¡r sekund":"pÃ¡r sekundami";case"m":return t?"minuta":u?"minutu":"minutou";case"mm":return t||u?f+(i(n)?"minuty":"minut"):f+"minutami";case"h":return t?"hodina":u?"hodinu":"hodinou";case"hh":return t||u?f+(i(n)?"hodiny":"hodin"):f+"hodinami";case"d":return t||u?"den":"dnem";case"dd":return t||u?f+(i(n)?"dny":"dnÃ­"):f+"dny";case"M":return t||u?"mÄ›sÃ­c":"mÄ›sÃ­cem";case"MM":return t||u?f+(i(n)?"mÄ›sÃ­ce":"mÄ›sÃ­cÅ¯"):f+"mÄ›sÃ­ci";case"y":return t||u?"rok":"rokem";case"yy":return t||u?f+(i(n)?"roky":"let"):f+"lety"}}var r="leden_Ãºnor_bÅ™ezen_duben_kvÄ›ten_Äerven_Äervenec_srpen_zÃ¡Å™Ã­_Å™Ã­jen_listopad_prosinec".split("_"),u="led_Ãºno_bÅ™e_dub_kvÄ›_Ävn_Ävc_srp_zÃ¡Å™_Å™Ã­j_lis_pro".split("_");return n.lang("cs",{months:r,monthsShort:u,monthsParse:function(n,t){for(var r=[],i=0;i<12;i++)r[i]=new RegExp("^"+n[i]+"$|^"+t[i]+"$","i");return r}(r,u),weekdays:"nedÄ›le_pondÄ›lÃ­_ÃºterÃ½_stÅ™eda_Ätvrtek_pÃ¡tek_sobota".split("_"),weekdaysShort:"ne_po_Ãºt_st_Ät_pÃ¡_so".split("_"),weekdaysMin:"ne_po_Ãºt_st_Ät_pÃ¡_so".split("_"),longDateFormat:{LT:"H.mm",L:"DD.Â MM.Â YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd D. MMMM YYYY LT"},calendar:{sameDay:"[dnes v] LT",nextDay:"[zÃ­tra v] LT",nextWeek:function(){switch(this.day()){case 0:return"[v nedÄ›li v] LT";case 1:case 2:return"[v] dddd [v] LT";case 3:return"[ve stÅ™edu v] LT";case 4:return"[ve Ätvrtek v] LT";case 5:return"[v pÃ¡tek v] LT";case 6:return"[v sobotu v] LT"}},lastDay:"[vÄera v] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulou nedÄ›li v] LT";case 1:case 2:return"[minulÃ©] dddd [v] LT";case 3:return"[minulou stÅ™edu v] LT";case 4:case 5:return"[minulÃ½] dddd [v] LT";case 6:return"[minulou sobotu v] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"pÅ™ed %s",s:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("cv",{months:"ÐºÄƒÑ€Ð»Ð°Ñ‡_Ð½Ð°Ñ€ÄƒÑ_Ð¿ÑƒÑˆ_Ð°ÐºÐ°_Ð¼Ð°Ð¹_Ã§Ä•Ñ€Ñ‚Ð¼Ðµ_ÑƒÑ‚Äƒ_Ã§ÑƒÑ€Ð»Ð°_Ð°Ð²ÄƒÐ½_ÑŽÐ¿Ð°_Ñ‡Ó³Ðº_Ñ€Ð°ÑˆÑ‚Ð°Ð²".split("_"),monthsShort:"ÐºÄƒÑ€_Ð½Ð°Ñ€_Ð¿ÑƒÑˆ_Ð°ÐºÐ°_Ð¼Ð°Ð¹_Ã§Ä•Ñ€_ÑƒÑ‚Äƒ_Ã§ÑƒÑ€_Ð°Ð²_ÑŽÐ¿Ð°_Ñ‡Ó³Ðº_Ñ€Ð°Ñˆ".split("_"),weekdays:"Ð²Ñ‹Ñ€ÑÐ°Ñ€Ð½Ð¸ÐºÑƒÐ½_Ñ‚ÑƒÐ½Ñ‚Ð¸ÐºÑƒÐ½_Ñ‹Ñ‚Ð»Ð°Ñ€Ð¸ÐºÑƒÐ½_ÑŽÐ½ÐºÑƒÐ½_ÐºÄ•Ã§Ð½ÐµÑ€Ð½Ð¸ÐºÑƒÐ½_ÑÑ€Ð½ÐµÐºÑƒÐ½_ÑˆÄƒÐ¼Ð°Ñ‚ÐºÑƒÐ½".split("_"),weekdaysShort:"Ð²Ñ‹Ñ€_Ñ‚ÑƒÐ½_Ñ‹Ñ‚Ð»_ÑŽÐ½_ÐºÄ•Ã§_ÑÑ€Ð½_ÑˆÄƒÐ¼".split("_"),weekdaysMin:"Ð²Ñ€_Ñ‚Ð½_Ñ‹Ñ‚_ÑŽÐ½_ÐºÃ§_ÑÑ€_ÑˆÐ¼".split("_"),longDateFormat:{LT:"HH:mm",L:"DD-MM-YYYY",LL:"YYYY [Ã§ÑƒÐ»Ñ…Ð¸] MMMM [ÑƒÐ¹ÄƒÑ…Ä•Ð½] D[-Ð¼Ä•ÑˆÄ•]",LLL:"YYYY [Ã§ÑƒÐ»Ñ…Ð¸] MMMM [ÑƒÐ¹ÄƒÑ…Ä•Ð½] D[-Ð¼Ä•ÑˆÄ•], LT",LLLL:"dddd, YYYY [Ã§ÑƒÐ»Ñ…Ð¸] MMMM [ÑƒÐ¹ÄƒÑ…Ä•Ð½] D[-Ð¼Ä•ÑˆÄ•], LT"},calendar:{sameDay:"[ÐŸÐ°ÑÐ½] LT [ÑÐµÑ…ÐµÑ‚Ñ€Ðµ]",nextDay:"[Ð«Ñ€Ð°Ð½] LT [ÑÐµÑ…ÐµÑ‚Ñ€Ðµ]",lastDay:"[Ä”Ð½ÐµÑ€] LT [ÑÐµÑ…ÐµÑ‚Ñ€Ðµ]",nextWeek:"[Ã‡Ð¸Ñ‚ÐµÑ] dddd LT [ÑÐµÑ…ÐµÑ‚Ñ€Ðµ]",lastWeek:"[Ð˜Ñ€Ñ‚Ð½Ä•] dddd LT [ÑÐµÑ…ÐµÑ‚Ñ€Ðµ]",sameElse:"L"},relativeTime:{future:function(n){var t=/ÑÐµÑ…ÐµÑ‚$/i.exec(n)?"Ñ€ÐµÐ½":/Ã§ÑƒÐ»$/i.exec(n)?"Ñ‚Ð°Ð½":"Ñ€Ð°Ð½";return n+t},past:"%s ÐºÐ°ÑÐ»Ð»Ð°",s:"Ð¿Ä•Ñ€-Ð¸Ðº Ã§ÐµÐºÐºÑƒÐ½Ñ‚",m:"Ð¿Ä•Ñ€ Ð¼Ð¸Ð½ÑƒÑ‚",mm:"%d Ð¼Ð¸Ð½ÑƒÑ‚",h:"Ð¿Ä•Ñ€ ÑÐµÑ…ÐµÑ‚",hh:"%d ÑÐµÑ…ÐµÑ‚",d:"Ð¿Ä•Ñ€ ÐºÑƒÐ½",dd:"%d ÐºÑƒÐ½",M:"Ð¿Ä•Ñ€ ÑƒÐ¹ÄƒÑ…",MM:"%d ÑƒÐ¹ÄƒÑ…",y:"Ð¿Ä•Ñ€ Ã§ÑƒÐ»",yy:"%d Ã§ÑƒÐ»"},ordinal:"%d-Ð¼Ä•Ñˆ",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("cy",{months:"Ionawr_Chwefror_Mawrth_Ebrill_Mai_Mehefin_Gorffennaf_Awst_Medi_Hydref_Tachwedd_Rhagfyr".split("_"),monthsShort:"Ion_Chwe_Maw_Ebr_Mai_Meh_Gor_Aws_Med_Hyd_Tach_Rhag".split("_"),weekdays:"Dydd Sul_Dydd Llun_Dydd Mawrth_Dydd Mercher_Dydd Iau_Dydd Gwener_Dydd Sadwrn".split("_"),weekdaysShort:"Sul_Llun_Maw_Mer_Iau_Gwe_Sad".split("_"),weekdaysMin:"Su_Ll_Ma_Me_Ia_Gw_Sa".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[Heddiw am] LT",nextDay:"[Yfory am] LT",nextWeek:"dddd [am] LT",lastDay:"[Ddoe am] LT",lastWeek:"dddd [diwethaf am] LT",sameElse:"L"},relativeTime:{future:"mewn %s",past:"%s yn Ã l",s:"ychydig eiliadau",m:"munud",mm:"%d munud",h:"awr",hh:"%d awr",d:"diwrnod",dd:"%d diwrnod",M:"mis",MM:"%d mis",y:"blwyddyn",yy:"%d flynedd"},ordinal:function(n){var t=n,i="";return t>20?i=t===40||t===50||t===60||t===80||t===100?"fed":"ain":t>0&&(i=["","af","il","ydd","ydd","ed","ed","ed","fed","fed","fed","eg","fed","eg","eg","fed","eg","eg","fed","eg","fed"][t]),n+i},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("da",{months:"januar_februar_marts_april_maj_juni_juli_august_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"sÃ¸ndag_mandag_tirsdag_onsdag_torsdag_fredag_lÃ¸rdag".split("_"),weekdaysShort:"sÃ¸n_man_tir_ons_tor_fre_lÃ¸r".split("_"),weekdaysMin:"sÃ¸_ma_ti_on_to_fr_lÃ¸".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D. MMMM, YYYY LT"},calendar:{sameDay:"[I dag kl.] LT",nextDay:"[I morgen kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[I gÃ¥r kl.] LT",lastWeek:"[sidste] dddd [kl] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"%s siden",s:"fÃ¥ sekunder",m:"et minut",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dage",M:"en mÃ¥ned",MM:"%d mÃ¥neder",y:"et Ã¥r",yy:"%d Ã¥r"},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){function t(n,t,i){var r={m:["eine Minute","einer Minute"],h:["eine Stunde","einer Stunde"],d:["ein Tag","einem Tag"],dd:[n+" Tage",n+" Tagen"],M:["ein Monat","einem Monat"],MM:[n+" Monate",n+" Monaten"],y:["ein Jahr","einem Jahr"],yy:[n+" Jahre",n+" Jahren"]};return t?r[i][0]:r[i][1]}return n.lang("de",{months:"Januar_Februar_MÃ¤rz_April_Mai_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Febr._Mrz._Apr._Mai_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),weekdays:"Sonntag_Montag_Dienstag_Mittwoch_Donnerstag_Freitag_Samstag".split("_"),weekdaysShort:"So._Mo._Di._Mi._Do._Fr._Sa.".split("_"),weekdaysMin:"So_Mo_Di_Mi_Do_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm [Uhr]",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd, D. MMMM YYYY LT"},calendar:{sameDay:"[Heute um] LT",sameElse:"L",nextDay:"[Morgen um] LT",nextWeek:"dddd [um] LT",lastDay:"[Gestern um] LT",lastWeek:"[letzten] dddd [um] LT"},relativeTime:{future:"in %s",past:"vor %s",s:"ein paar Sekunden",m:t,mm:"%d Minuten",h:t,hh:"%d Stunden",d:t,dd:t,M:t,MM:t,y:t,yy:t},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("el",{monthsNominativeEl:"Î™Î±Î½Î¿Ï…Î¬ÏÎ¹Î¿Ï‚_Î¦ÎµÎ²ÏÎ¿Ï…Î¬ÏÎ¹Î¿Ï‚_ÎœÎ¬ÏÏ„Î¹Î¿Ï‚_Î‘Ï€ÏÎ¯Î»Î¹Î¿Ï‚_ÎœÎ¬Î¹Î¿Ï‚_Î™Î¿ÏÎ½Î¹Î¿Ï‚_Î™Î¿ÏÎ»Î¹Î¿Ï‚_Î‘ÏÎ³Î¿Ï…ÏƒÏ„Î¿Ï‚_Î£ÎµÏ€Ï„Î­Î¼Î²ÏÎ¹Î¿Ï‚_ÎŸÎºÏ„ÏŽÎ²ÏÎ¹Î¿Ï‚_ÎÎ¿Î­Î¼Î²ÏÎ¹Î¿Ï‚_Î”ÎµÎºÎ­Î¼Î²ÏÎ¹Î¿Ï‚".split("_"),monthsGenitiveEl:"Î™Î±Î½Î¿Ï…Î±ÏÎ¯Î¿Ï…_Î¦ÎµÎ²ÏÎ¿Ï…Î±ÏÎ¯Î¿Ï…_ÎœÎ±ÏÏ„Î¯Î¿Ï…_Î‘Ï€ÏÎ¹Î»Î¯Î¿Ï…_ÎœÎ±ÎÎ¿Ï…_Î™Î¿Ï…Î½Î¯Î¿Ï…_Î™Î¿Ï…Î»Î¯Î¿Ï…_Î‘Ï…Î³Î¿ÏÏƒÏ„Î¿Ï…_Î£ÎµÏ€Ï„ÎµÎ¼Î²ÏÎ¯Î¿Ï…_ÎŸÎºÏ„Ï‰Î²ÏÎ¯Î¿Ï…_ÎÎ¿ÎµÎ¼Î²ÏÎ¯Î¿Ï…_Î”ÎµÎºÎµÎ¼Î²ÏÎ¯Î¿Ï…".split("_"),months:function(n,t){return/D/.test(t.substring(0,t.indexOf("MMMM")))?this._monthsGenitiveEl[n.month()]:this._monthsNominativeEl[n.month()]},monthsShort:"Î™Î±Î½_Î¦ÎµÎ²_ÎœÎ±Ï_Î‘Ï€Ï_ÎœÎ±ÏŠ_Î™Î¿Ï…Î½_Î™Î¿Ï…Î»_Î‘Ï…Î³_Î£ÎµÏ€_ÎŸÎºÏ„_ÎÎ¿Îµ_Î”ÎµÎº".split("_"),weekdays:"ÎšÏ…ÏÎ¹Î±ÎºÎ®_Î”ÎµÏ…Ï„Î­ÏÎ±_Î¤ÏÎ¯Ï„Î·_Î¤ÎµÏ„Î¬ÏÏ„Î·_Î Î­Î¼Ï€Ï„Î·_Î Î±ÏÎ±ÏƒÎºÎµÏ…Î®_Î£Î¬Î²Î²Î±Ï„Î¿".split("_"),weekdaysShort:"ÎšÏ…Ï_Î”ÎµÏ…_Î¤ÏÎ¹_Î¤ÎµÏ„_Î ÎµÎ¼_Î Î±Ï_Î£Î±Î²".split("_"),weekdaysMin:"ÎšÏ…_Î”Îµ_Î¤Ï_Î¤Îµ_Î Îµ_Î Î±_Î£Î±".split("_"),meridiem:function(n,t,i){return n>11?i?"Î¼Î¼":"ÎœÎœ":i?"Ï€Î¼":"Î Îœ"},longDateFormat:{LT:"h:mm A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendarEl:{sameDay:"[Î£Î®Î¼ÎµÏÎ± {}] LT",nextDay:"[Î‘ÏÏÎ¹Î¿ {}] LT",nextWeek:"dddd [{}] LT",lastDay:"[Î§Î¸ÎµÏ‚ {}] LT",lastWeek:"[Ï„Î·Î½ Ï€ÏÎ¿Î·Î³Î¿ÏÎ¼ÎµÎ½Î·] dddd [{}] LT",sameElse:"L"},calendar:function(n,t){var i=this._calendarEl[n],r=t&&t.hours();return i.replace("{}",r%12==1?"ÏƒÏ„Î·":"ÏƒÏ„Î¹Ï‚")},relativeTime:{future:"ÏƒÎµ %s",past:"%s Ï€ÏÎ¹Î½",s:"Î´ÎµÏ…Ï„ÎµÏÏŒÎ»ÎµÏ€Ï„Î±",m:"Î­Î½Î± Î»ÎµÏ€Ï„ÏŒ",mm:"%d Î»ÎµÏ€Ï„Î¬",h:"Î¼Î¯Î± ÏŽÏÎ±",hh:"%d ÏŽÏÎµÏ‚",d:"Î¼Î¯Î± Î¼Î­ÏÎ±",dd:"%d Î¼Î­ÏÎµÏ‚",M:"Î­Î½Î±Ï‚ Î¼Î®Î½Î±Ï‚",MM:"%d Î¼Î®Î½ÎµÏ‚",y:"Î­Î½Î±Ï‚ Ï‡ÏÏŒÎ½Î¿Ï‚",yy:"%d Ï‡ÏÏŒÎ½Î¹Î±"},ordinal:function(n){return n+"Î·"},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("en-au",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("en-ca",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"h:mm A",L:"YYYY-MM-DD",LL:"D MMMM, YYYY",LLL:"D MMMM, YYYY LT",LLLL:"dddd, D MMMM, YYYY LT"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i}})}),function(n){n(t)}(function(n){return n.lang("en-gb",{months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"th":t===1?"st":t===2?"nd":t===3?"rd":"th";return n+i},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("eo",{months:"januaro_februaro_marto_aprilo_majo_junio_julio_aÅ­gusto_septembro_oktobro_novembro_decembro".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aÅ­g_sep_okt_nov_dec".split("_"),weekdays:"DimanÄ‰o_Lundo_Mardo_Merkredo_Ä´aÅ­do_Vendredo_Sabato".split("_"),weekdaysShort:"Dim_Lun_Mard_Merk_Ä´aÅ­_Ven_Sab".split("_"),weekdaysMin:"Di_Lu_Ma_Me_Ä´a_Ve_Sa".split("_"),longDateFormat:{LT:"HH:mm",L:"YYYY-MM-DD",LL:"D[-an de] MMMM, YYYY",LLL:"D[-an de] MMMM, YYYY LT",LLLL:"dddd, [la] D[-an de] MMMM, YYYY LT"},meridiem:function(n,t,i){return n>11?i?"p.t.m.":"P.T.M.":i?"a.t.m.":"A.T.M."},calendar:{sameDay:"[HodiaÅ­ je] LT",nextDay:"[MorgaÅ­ je] LT",nextWeek:"dddd [je] LT",lastDay:"[HieraÅ­ je] LT",lastWeek:"[pasinta] dddd [je] LT",sameElse:"L"},relativeTime:{future:"je %s",past:"antaÅ­ %s",s:"sekundoj",m:"minuto",mm:"%d minutoj",h:"horo",hh:"%d horoj",d:"tago",dd:"%d tagoj",M:"monato",MM:"%d monatoj",y:"jaro",yy:"%d jaroj"},ordinal:"%da",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){var t="ene._feb._mar._abr._may._jun._jul._ago._sep._oct._nov._dic.".split("_"),i="ene_feb_mar_abr_may_jun_jul_ago_sep_oct_nov_dic".split("_");return n.lang("es",{months:"enero_febrero_marzo_abril_mayo_junio_julio_agosto_septiembre_octubre_noviembre_diciembre".split("_"),monthsShort:function(n,r){return/-MMM-/.test(r)?i[n.month()]:t[n.month()]},weekdays:"domingo_lunes_martes_miÃ©rcoles_jueves_viernes_sÃ¡bado".split("_"),weekdaysShort:"dom._lun._mar._miÃ©._jue._vie._sÃ¡b.".split("_"),weekdaysMin:"Do_Lu_Ma_Mi_Ju_Vi_SÃ¡".split("_"),longDateFormat:{LT:"H:mm",L:"DD/MM/YYYY",LL:"D [de] MMMM [del] YYYY",LLL:"D [de] MMMM [del] YYYY LT",LLLL:"dddd, D [de] MMMM [del] YYYY LT"},calendar:{sameDay:function(){return"[hoy a la"+(this.hours()!==1?"s":"")+"] LT"},nextDay:function(){return"[maÃ±ana a la"+(this.hours()!==1?"s":"")+"] LT"},nextWeek:function(){return"dddd [a la"+(this.hours()!==1?"s":"")+"] LT"},lastDay:function(){return"[ayer a la"+(this.hours()!==1?"s":"")+"] LT"},lastWeek:function(){return"[el] dddd [pasado a la"+(this.hours()!==1?"s":"")+"] LT"},sameElse:"L"},relativeTime:{future:"en %s",past:"hace %s",s:"unos segundos",m:"un minuto",mm:"%d minutos",h:"una hora",hh:"%d horas",d:"un dÃ­a",dd:"%d dÃ­as",M:"un mes",MM:"%d meses",y:"un aÃ±o",yy:"%d aÃ±os"},ordinal:"%dÂº",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){function t(n,t,i,r){var u={s:["mÃµne sekundi","mÃµni sekund","paar sekundit"],m:["Ã¼he minuti","Ã¼ks minut"],mm:[n+" minuti",n+" minutit"],h:["Ã¼he tunni","tund aega","Ã¼ks tund"],hh:[n+" tunni",n+" tundi"],d:["Ã¼he pÃ¤eva","Ã¼ks pÃ¤ev"],M:["kuu aja","kuu aega","Ã¼ks kuu"],MM:[n+" kuu",n+" kuud"],y:["Ã¼he aasta","aasta","Ã¼ks aasta"],yy:[n+" aasta",n+" aastat"]};return t?u[i][2]?u[i][2]:u[i][1]:r?u[i][0]:u[i][1]}return n.lang("et",{months:"jaanuar_veebruar_mÃ¤rts_aprill_mai_juuni_juuli_august_september_oktoober_november_detsember".split("_"),monthsShort:"jaan_veebr_mÃ¤rts_apr_mai_juuni_juuli_aug_sept_okt_nov_dets".split("_"),weekdays:"pÃ¼hapÃ¤ev_esmaspÃ¤ev_teisipÃ¤ev_kolmapÃ¤ev_neljapÃ¤ev_reede_laupÃ¤ev".split("_"),weekdaysShort:"P_E_T_K_N_R_L".split("_"),weekdaysMin:"P_E_T_K_N_R_L".split("_"),longDateFormat:{LT:"H:mm",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd, D. MMMM YYYY LT"},calendar:{sameDay:"[TÃ¤na,] LT",nextDay:"[Homme,] LT",nextWeek:"[JÃ¤rgmine] dddd LT",lastDay:"[Eile,] LT",lastWeek:"[Eelmine] dddd LT",sameElse:"L"},relativeTime:{future:"%s pÃ¤rast",past:"%s tagasi",s:t,m:t,mm:t,h:t,hh:t,d:t,dd:"%d pÃ¤eva",M:t,MM:t,y:t,yy:t},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("eu",{months:"urtarrila_otsaila_martxoa_apirila_maiatza_ekaina_uztaila_abuztua_iraila_urria_azaroa_abendua".split("_"),monthsShort:"urt._ots._mar._api._mai._eka._uzt._abu._ira._urr._aza._abe.".split("_"),weekdays:"igandea_astelehena_asteartea_asteazkena_osteguna_ostirala_larunbata".split("_"),weekdaysShort:"ig._al._ar._az._og._ol._lr.".split("_"),weekdaysMin:"ig_al_ar_az_og_ol_lr".split("_"),longDateFormat:{LT:"HH:mm",L:"YYYY-MM-DD",LL:"YYYY[ko] MMMM[ren] D[a]",LLL:"YYYY[ko] MMMM[ren] D[a] LT",LLLL:"dddd, YYYY[ko] MMMM[ren] D[a] LT",l:"YYYY-M-D",ll:"YYYY[ko] MMM D[a]",lll:"YYYY[ko] MMM D[a] LT",llll:"ddd, YYYY[ko] MMM D[a] LT"},calendar:{sameDay:"[gaur] LT[etan]",nextDay:"[bihar] LT[etan]",nextWeek:"dddd LT[etan]",lastDay:"[atzo] LT[etan]",lastWeek:"[aurreko] dddd LT[etan]",sameElse:"L"},relativeTime:{future:"%s barru",past:"duela %s",s:"segundo batzuk",m:"minutu bat",mm:"%d minutu",h:"ordu bat",hh:"%d ordu",d:"egun bat",dd:"%d egun",M:"hilabete bat",MM:"%d hilabete",y:"urte bat",yy:"%d urte"},ordinal:"%d.",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){var t={"1":"Û±","2":"Û²","3":"Û³","4":"Û´","5":"Ûµ","6":"Û¶","7":"Û·","8":"Û¸","9":"Û¹","0":"Û°"},i={"Û±":"1","Û²":"2","Û³":"3","Û´":"4","Ûµ":"5","Û¶":"6","Û·":"7","Û¸":"8","Û¹":"9","Û°":"0"};return n.lang("fa",{months:"Ú˜Ø§Ù†ÙˆÛŒÙ‡_ÙÙˆØ±ÛŒÙ‡_Ù…Ø§Ø±Ø³_Ø¢ÙˆØ±ÛŒÙ„_Ù…Ù‡_Ú˜ÙˆØ¦Ù†_Ú˜ÙˆØ¦ÛŒÙ‡_Ø§ÙˆØª_Ø³Ù¾ØªØ§Ù…Ø¨Ø±_Ø§Ú©ØªØ¨Ø±_Ù†ÙˆØ§Ù…Ø¨Ø±_Ø¯Ø³Ø§Ù…Ø¨Ø±".split("_"),monthsShort:"Ú˜Ø§Ù†ÙˆÛŒÙ‡_ÙÙˆØ±ÛŒÙ‡_Ù…Ø§Ø±Ø³_Ø¢ÙˆØ±ÛŒÙ„_Ù…Ù‡_Ú˜ÙˆØ¦Ù†_Ú˜ÙˆØ¦ÛŒÙ‡_Ø§ÙˆØª_Ø³Ù¾ØªØ§Ù…Ø¨Ø±_Ø§Ú©ØªØ¨Ø±_Ù†ÙˆØ§Ù…Ø¨Ø±_Ø¯Ø³Ø§Ù…Ø¨Ø±".split("_"),weekdays:"ÛŒÚ©‌Ø´Ù†Ø¨Ù‡_Ø¯ÙˆØ´Ù†Ø¨Ù‡_Ø³Ù‡‌Ø´Ù†Ø¨Ù‡_Ú†Ù‡Ø§Ø±Ø´Ù†Ø¨Ù‡_Ù¾Ù†Ø¬‌Ø´Ù†Ø¨Ù‡_Ø¬Ù…Ø¹Ù‡_Ø´Ù†Ø¨Ù‡".split("_"),weekdaysShort:"ÛŒÚ©‌Ø´Ù†Ø¨Ù‡_Ø¯ÙˆØ´Ù†Ø¨Ù‡_Ø³Ù‡‌Ø´Ù†Ø¨Ù‡_Ú†Ù‡Ø§Ø±Ø´Ù†Ø¨Ù‡_Ù¾Ù†Ø¬‌Ø´Ù†Ø¨Ù‡_Ø¬Ù…Ø¹Ù‡_Ø´Ù†Ø¨Ù‡".split("_"),weekdaysMin:"ÛŒ_Ø¯_Ø³_Ú†_Ù¾_Ø¬_Ø´".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},meridiem:function(n){return n<12?"Ù‚Ø¨Ù„ Ø§Ø² Ø¸Ù‡Ø±":"Ø¨Ø¹Ø¯ Ø§Ø² Ø¸Ù‡Ø±"},calendar:{sameDay:"[Ø§Ù…Ø±ÙˆØ² Ø³Ø§Ø¹Øª] LT",nextDay:"[ÙØ±Ø¯Ø§ Ø³Ø§Ø¹Øª] LT",nextWeek:"dddd [Ø³Ø§Ø¹Øª] LT",lastDay:"[Ø¯ÛŒØ±ÙˆØ² Ø³Ø§Ø¹Øª] LT",lastWeek:"dddd [Ù¾ÛŒØ´] [Ø³Ø§Ø¹Øª] LT",sameElse:"L"},relativeTime:{future:"Ø¯Ø± %s",past:"%s Ù¾ÛŒØ´",s:"Ú†Ù†Ø¯ÛŒÙ† Ø«Ø§Ù†ÛŒÙ‡",m:"ÛŒÚ© Ø¯Ù‚ÛŒÙ‚Ù‡",mm:"%d Ø¯Ù‚ÛŒÙ‚Ù‡",h:"ÛŒÚ© Ø³Ø§Ø¹Øª",hh:"%d Ø³Ø§Ø¹Øª",d:"ÛŒÚ© Ø±ÙˆØ²",dd:"%d Ø±ÙˆØ²",M:"ÛŒÚ© Ù…Ø§Ù‡",MM:"%d Ù…Ø§Ù‡",y:"ÛŒÚ© Ø³Ø§Ù„",yy:"%d Ø³Ø§Ù„"},preparse:function(n){return n.replace(/[Û°-Û¹]/g,function(n){return i[n]}).replace(/ØŒ/g,",")},postformat:function(n){return n.replace(/\d/g,function(n){return t[n]}).replace(/,/g,"ØŒ")},ordinal:"%dÙ…",week:{dow:6,doy:12}})}),function(n){n(t)}(function(n){function t(n,t,i,r){var f="";switch(i){case"s":return r?"muutaman sekunnin":"muutama sekunti";case"m":return r?"minuutin":"minuutti";case"mm":f=r?"minuutin":"minuuttia";break;case"h":return r?"tunnin":"tunti";case"hh":f=r?"tunnin":"tuntia";break;case"d":return r?"pÃ¤ivÃ¤n":"pÃ¤ivÃ¤";case"dd":f=r?"pÃ¤ivÃ¤n":"pÃ¤ivÃ¤Ã¤";break;case"M":return r?"kuukauden":"kuukausi";case"MM":f=r?"kuukauden":"kuukautta";break;case"y":return r?"vuoden":"vuosi";case"yy":f=r?"vuoden":"vuotta"}return u(n,r)+" "+f}function u(n,t){return n<10?t?r[n]:i[n]:n}var i="nolla yksi kaksi kolme neljÃ¤ viisi kuusi seitsemÃ¤n kahdeksan yhdeksÃ¤n".split(" "),r=["nolla","yhden","kahden","kolmen","neljÃ¤n","viiden","kuuden",i[7],i[8],i[9]];return n.lang("fi",{months:"tammikuu_helmikuu_maaliskuu_huhtikuu_toukokuu_kesÃ¤kuu_heinÃ¤kuu_elokuu_syyskuu_lokakuu_marraskuu_joulukuu".split("_"),monthsShort:"tammi_helmi_maalis_huhti_touko_kesÃ¤_heinÃ¤_elo_syys_loka_marras_joulu".split("_"),weekdays:"sunnuntai_maanantai_tiistai_keskiviikko_torstai_perjantai_lauantai".split("_"),weekdaysShort:"su_ma_ti_ke_to_pe_la".split("_"),weekdaysMin:"su_ma_ti_ke_to_pe_la".split("_"),longDateFormat:{LT:"HH.mm",L:"DD.MM.YYYY",LL:"Do MMMM[ta] YYYY",LLL:"Do MMMM[ta] YYYY, [klo] LT",LLLL:"dddd, Do MMMM[ta] YYYY, [klo] LT",l:"D.M.YYYY",ll:"Do MMM YYYY",lll:"Do MMM YYYY, [klo] LT",llll:"ddd, Do MMM YYYY, [klo] LT"},calendar:{sameDay:"[tÃ¤nÃ¤Ã¤n] [klo] LT",nextDay:"[huomenna] [klo] LT",nextWeek:"dddd [klo] LT",lastDay:"[eilen] [klo] LT",lastWeek:"[viime] dddd[na] [klo] LT",sameElse:"L"},relativeTime:{future:"%s pÃ¤Ã¤stÃ¤",past:"%s sitten",s:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("fo",{months:"januar_februar_mars_aprÃ­l_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sunnudagur_mÃ¡nadagur_tÃ½sdagur_mikudagur_hÃ³sdagur_frÃ­ggjadagur_leygardagur".split("_"),weekdaysShort:"sun_mÃ¡n_tÃ½s_mik_hÃ³s_frÃ­_ley".split("_"),weekdaysMin:"su_mÃ¡_tÃ½_mi_hÃ³_fr_le".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D. MMMM, YYYY LT"},calendar:{sameDay:"[Ã dag kl.] LT",nextDay:"[Ã morgin kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[Ã gjÃ¡r kl.] LT",lastWeek:"[sÃ­Ã°stu] dddd [kl] LT",sameElse:"L"},relativeTime:{future:"um %s",past:"%s sÃ­Ã°ani",s:"fÃ¡ sekund",m:"ein minutt",mm:"%d minuttir",h:"ein tÃ­mi",hh:"%d tÃ­mar",d:"ein dagur",dd:"%d dagar",M:"ein mÃ¡naÃ°i",MM:"%d mÃ¡naÃ°ir",y:"eitt Ã¡r",yy:"%d Ã¡r"},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("fr-ca",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._fÃ©vr._mars_avr._mai_juin_juil._aoÃ»t_sept._oct._nov._dÃ©c.".split("_"),weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"Di_Lu_Ma_Me_Je_Ve_Sa".split("_"),longDateFormat:{LT:"HH:mm",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:"[Aujourd'hui Ã ] LT",nextDay:"[Demain Ã ] LT",nextWeek:"dddd [Ã ] LT",lastDay:"[Hier Ã ] LT",lastWeek:"dddd [dernier Ã ] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinal:function(n){return n+(n===1?"er":"")}})}),function(n){n(t)}(function(n){return n.lang("fr",{months:"janvier_février_mars_avril_mai_juin_juillet_août_septembre_octobre_novembre_décembre".split("_"),monthsShort:"janv._fÃ©vr._mars_avr._mai_juin_juil._aoÃ»t_sept._oct._nov._dÃ©c.".split("_"),weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"Di_Lu_Ma_Me_Je_Ve_Sa".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:"[Aujourd'hui Ã ] LT",nextDay:"[Demain Ã ] LT",nextWeek:"dddd [Ã ] LT",lastDay:"[Hier Ã ] LT",lastWeek:"dddd [dernier Ã ] LT",sameElse:"L"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinal:function(n){return n+(n===1?"er":"")},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("gl",{months:"Xaneiro_Febreiro_Marzo_Abril_Maio_XuÃ±o_Xullo_Agosto_Setembro_Outubro_Novembro_Decembro".split("_"),monthsShort:"Xan._Feb._Mar._Abr._Mai._XuÃ±._Xul._Ago._Set._Out._Nov._Dec.".split("_"),weekdays:"Domingo_Luns_Martes_MÃ©rcores_Xoves_Venres_SÃ¡bado".split("_"),weekdaysShort:"Dom._Lun._Mar._MÃ©r._Xov._Ven._SÃ¡b.".split("_"),weekdaysMin:"Do_Lu_Ma_MÃ©_Xo_Ve_SÃ¡".split("_"),longDateFormat:{LT:"H:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:function(){return"[hoxe "+(this.hours()!==1?"Ã¡s":"Ã¡")+"] LT"},nextDay:function(){return"[maÃ±Ã¡ "+(this.hours()!==1?"Ã¡s":"Ã¡")+"] LT"},nextWeek:function(){return"dddd ["+(this.hours()!==1?"Ã¡s":"a")+"] LT"},lastDay:function(){return"[onte "+(this.hours()!==1?"Ã¡":"a")+"] LT"},lastWeek:function(){return"[o] dddd [pasado "+(this.hours()!==1?"Ã¡s":"a")+"] LT"},sameElse:"L"},relativeTime:{future:function(n){return n==="uns segundos"?"nuns segundos":"en "+n},past:"hai %s",s:"uns segundos",m:"un minuto",mm:"%d minutos",h:"unha hora",hh:"%d horas",d:"un dÃ­a",dd:"%d dÃ­as",M:"un mes",MM:"%d meses",y:"un ano",yy:"%d anos"},ordinal:"%dÂº",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("he",{months:"×™× ×•××¨_×¤×‘×¨×•××¨_×ž×¨×¥_××¤×¨×™×œ_×ž××™_×™×•× ×™_×™×•×œ×™_××•×’×•×¡×˜_×¡×¤×˜×ž×‘×¨_××•×§×˜×•×‘×¨_× ×•×‘×ž×‘×¨_×“×¦×ž×‘×¨".split("_"),monthsShort:"×™× ×•×³_×¤×‘×¨×³_×ž×¨×¥_××¤×¨×³_×ž××™_×™×•× ×™_×™×•×œ×™_××•×’×³_×¡×¤×˜×³_××•×§×³_× ×•×‘×³_×“×¦×ž×³".split("_"),weekdays:"×¨××©×•×Ÿ_×©× ×™_×©×œ×™×©×™_×¨×‘×™×¢×™_×—×ž×™×©×™_×©×™×©×™_×©×‘×ª".split("_"),weekdaysShort:"××³_×‘×³_×’×³_×“×³_×”×³_×•×³_×©×³".split("_"),weekdaysMin:"×_×‘_×’_×“_×”_×•_×©".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D [×‘]MMMM YYYY",LLL:"D [×‘]MMMM YYYY LT",LLLL:"dddd, D [×‘]MMMM YYYY LT",l:"D/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY LT",llll:"ddd, D MMM YYYY LT"},calendar:{sameDay:"[×”×™×•× ×‘Ö¾]LT",nextDay:"[×ž×—×¨ ×‘Ö¾]LT",nextWeek:"dddd [×‘×©×¢×”] LT",lastDay:"[××ª×ž×•×œ ×‘Ö¾]LT",lastWeek:"[×‘×™×•×] dddd [×”××—×¨×•×Ÿ ×‘×©×¢×”] LT",sameElse:"L"},relativeTime:{future:"×‘×¢×•×“ %s",past:"×œ×¤× ×™ %s",s:"×ž×¡×¤×¨ ×©× ×™×•×ª",m:"×“×§×”",mm:"%d ×“×§×•×ª",h:"×©×¢×”",hh:function(n){return n===2?"×©×¢×ª×™×™×":n+" ×©×¢×•×ª"},d:"×™×•×",dd:function(n){return n===2?"×™×•×ž×™×™×":n+" ×™×ž×™×"},M:"×—×•×“×©",MM:function(n){return n===2?"×—×•×“×©×™×™×":n+" ×—×•×“×©×™×"},y:"×©× ×”",yy:function(n){return n===2?"×©× ×ª×™×™×":n+" ×©× ×™×"}}})}),function(n){n(t)}(function(n){var t={"1":"à¥§","2":"à¥¨","3":"à¥©","4":"à¥ª","5":"à¥«","6":"à¥¬","7":"à¥­","8":"à¥®","9":"à¥¯","0":"à¥¦"},i={"à¥§":"1","à¥¨":"2","à¥©":"3","à¥ª":"4","à¥«":"5","à¥¬":"6","à¥­":"7","à¥®":"8","à¥¯":"9","à¥¦":"0"};return n.lang("hi",{months:"à¤œà¤¨à¤µà¤°à¥€_à¤«à¤¼à¤°à¤µà¤°à¥€_à¤®à¤¾à¤°à¥à¤š_à¤…à¤ªà¥à¤°à¥ˆà¤²_à¤®à¤ˆ_à¤œà¥‚à¤¨_à¤œà¥à¤²à¤¾à¤ˆ_à¤…à¤—à¤¸à¥à¤¤_à¤¸à¤¿à¤¤à¤®à¥à¤¬à¤°_à¤…à¤•à¥à¤Ÿà¥‚à¤¬à¤°_à¤¨à¤µà¤®à¥à¤¬à¤°_à¤¦à¤¿à¤¸à¤®à¥à¤¬à¤°".split("_"),monthsShort:"à¤œà¤¨._à¤«à¤¼à¤°._à¤®à¤¾à¤°à¥à¤š_à¤…à¤ªà¥à¤°à¥ˆ._à¤®à¤ˆ_à¤œà¥‚à¤¨_à¤œà¥à¤²._à¤…à¤—._à¤¸à¤¿à¤¤._à¤…à¤•à¥à¤Ÿà¥‚._à¤¨à¤µ._à¤¦à¤¿à¤¸.".split("_"),weekdays:"à¤°à¤µà¤¿à¤µà¤¾à¤°_à¤¸à¥‹à¤®à¤µà¤¾à¤°_à¤®à¤‚à¤—à¤²à¤µà¤¾à¤°_à¤¬à¥à¤§à¤µà¤¾à¤°_à¤—à¥à¤°à¥‚à¤µà¤¾à¤°_à¤¶à¥à¤•à¥à¤°à¤µà¤¾à¤°_à¤¶à¤¨à¤¿à¤µà¤¾à¤°".split("_"),weekdaysShort:"à¤°à¤µà¤¿_à¤¸à¥‹à¤®_à¤®à¤‚à¤—à¤²_à¤¬à¥à¤§_à¤—à¥à¤°à¥‚_à¤¶à¥à¤•à¥à¤°_à¤¶à¤¨à¤¿".split("_"),weekdaysMin:"à¤°_à¤¸à¥‹_à¤®à¤‚_à¤¬à¥_à¤—à¥_à¤¶à¥_à¤¶".split("_"),longDateFormat:{LT:"A h:mm à¤¬à¤œà¥‡",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, LT",LLLL:"dddd, D MMMM YYYY, LT"},calendar:{sameDay:"[à¤†à¤œ] LT",nextDay:"[à¤•à¤²] LT",nextWeek:"dddd, LT",lastDay:"[à¤•à¤²] LT",lastWeek:"[à¤ªà¤¿à¤›à¤²à¥‡] dddd, LT",sameElse:"L"},relativeTime:{future:"%s à¤®à¥‡à¤‚",past:"%s à¤ªà¤¹à¤²à¥‡",s:"à¤•à¥à¤› à¤¹à¥€ à¤•à¥à¤·à¤£",m:"à¤à¤• à¤®à¤¿à¤¨à¤Ÿ",mm:"%d à¤®à¤¿à¤¨à¤Ÿ",h:"à¤à¤• à¤˜à¤‚à¤Ÿà¤¾",hh:"%d à¤˜à¤‚à¤Ÿà¥‡",d:"à¤à¤• à¤¦à¤¿à¤¨",dd:"%d à¤¦à¤¿à¤¨",M:"à¤à¤• à¤®à¤¹à¥€à¤¨à¥‡",MM:"%d à¤®à¤¹à¥€à¤¨à¥‡",y:"à¤à¤• à¤µà¤°à¥à¤·",yy:"%d à¤µà¤°à¥à¤·"},preparse:function(n){return n.replace(/[à¥§à¥¨à¥©à¥ªà¥«à¥¬à¥­à¥®à¥¯à¥¦]/g,function(n){return i[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return t[n]})},meridiem:function(n){return n<4?"à¤°à¤¾à¤¤":n<10?"à¤¸à¥à¤¬à¤¹":n<17?"à¤¦à¥‹à¤ªà¤¹à¤°":n<20?"à¤¶à¤¾à¤®":"à¤°à¤¾à¤¤"},week:{dow:0,doy:6}})}),function(n){n(t)}(function(n){function t(n,t,i){var r=n+" ";switch(i){case"m":return t?"jedna minuta":"jedne minute";case"mm":return r+(n===1?"minuta":n===2||n===3||n===4?"minute":"minuta");case"h":return t?"jedan sat":"jednog sata";case"hh":return r+(n===1?"sat":n===2||n===3||n===4?"sata":"sati");case"dd":return r+(n===1?"dan":"dana");case"MM":return r+(n===1?"mjesec":n===2||n===3||n===4?"mjeseca":"mjeseci");case"yy":return r+(n===1?"godina":n===2||n===3||n===4?"godine":"godina")}}return n.lang("hr",{months:"sjeÄanj_veljaÄa_oÅ¾ujak_travanj_svibanj_lipanj_srpanj_kolovoz_rujan_listopad_studeni_prosinac".split("_"),monthsShort:"sje._vel._oÅ¾u._tra._svi._lip._srp._kol._ruj._lis._stu._pro.".split("_"),weekdays:"nedjelja_ponedjeljak_utorak_srijeda_Äetvrtak_petak_subota".split("_"),weekdaysShort:"ned._pon._uto._sri._Äet._pet._sub.".split("_"),weekdaysMin:"ne_po_ut_sr_Äe_pe_su".split("_"),longDateFormat:{LT:"H:mm",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd, D. MMMM YYYY LT"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedjelju] [u] LT";case 3:return"[u] [srijedu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juÄer u] LT",lastWeek:function(){switch(this.day()){case 0:case 3:return"[proÅ¡lu] dddd [u] LT";case 6:return"[proÅ¡le] [subote] [u] LT";case 1:case 2:case 4:case 5:return"[proÅ¡li] dddd [u] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"prije %s",s:"par sekundi",m:t,mm:t,h:t,hh:t,d:"dan",dd:t,M:"mjesec",MM:t,y:"godinu",yy:t},ordinal:"%d.",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){function t(n,t,i,r){var u=n;switch(i){case"s":return r||t?"nÃ©hÃ¡ny mÃ¡sodperc":"nÃ©hÃ¡ny mÃ¡sodperce";case"m":return"egy"+(r||t?" perc":" perce");case"mm":return u+(r||t?" perc":" perce");case"h":return"egy"+(r||t?" Ã³ra":" Ã³rÃ¡ja");case"hh":return u+(r||t?" Ã³ra":" Ã³rÃ¡ja");case"d":return"egy"+(r||t?" nap":" napja");case"dd":return u+(r||t?" nap":" napja");case"M":return"egy"+(r||t?" hÃ³nap":" hÃ³napja");case"MM":return u+(r||t?" hÃ³nap":" hÃ³napja");case"y":return"egy"+(r||t?" Ã©v":" Ã©ve");case"yy":return u+(r||t?" Ã©v":" Ã©ve")}return""}function i(n){return(n?"":"[mÃºlt] ")+"["+r[this.day()]+"] LT[-kor]"}var r="vasÃ¡rnap hÃ©tfÅ‘n kedden szerdÃ¡n csÃ¼tÃ¶rtÃ¶kÃ¶n pÃ©nteken szombaton".split(" ");return n.lang("hu",{months:"januÃ¡r_februÃ¡r_mÃ¡rcius_Ã¡prilis_mÃ¡jus_jÃºnius_jÃºlius_augusztus_szeptember_oktÃ³ber_november_december".split("_"),monthsShort:"jan_feb_mÃ¡rc_Ã¡pr_mÃ¡j_jÃºn_jÃºl_aug_szept_okt_nov_dec".split("_"),weekdays:"vasÃ¡rnap_hÃ©tfÅ‘_kedd_szerda_csÃ¼tÃ¶rtÃ¶k_pÃ©ntek_szombat".split("_"),weekdaysShort:"vas_hÃ©t_kedd_sze_csÃ¼t_pÃ©n_szo".split("_"),weekdaysMin:"v_h_k_sze_cs_p_szo".split("_"),longDateFormat:{LT:"H:mm",L:"YYYY.MM.DD.",LL:"YYYY. MMMM D.",LLL:"YYYY. MMMM D., LT",LLLL:"YYYY. MMMM D., dddd LT"},meridiem:function(n,t,i){return n<12?i===!0?"de":"DE":i===!0?"du":"DU"},calendar:{sameDay:"[ma] LT[-kor]",nextDay:"[holnap] LT[-kor]",nextWeek:function(){return i.call(this,!0)},lastDay:"[tegnap] LT[-kor]",lastWeek:function(){return i.call(this,!1)},sameElse:"L"},relativeTime:{future:"%s mÃºlva",past:"%s",s:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},ordinal:"%d.",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){function t(n,t){var i={nominative:"Õ°Õ¸Ö‚Õ¶Õ¾Õ¡Ö€_ÖƒÕ¥Õ¿Ö€Õ¾Õ¡Ö€_Õ´Õ¡Ö€Õ¿_Õ¡ÕºÖ€Õ«Õ¬_Õ´Õ¡ÕµÕ«Õ½_Õ°Õ¸Ö‚Õ¶Õ«Õ½_Õ°Õ¸Ö‚Õ¬Õ«Õ½_Ö…Õ£Õ¸Õ½Õ¿Õ¸Õ½_Õ½Õ¥ÕºÕ¿Õ¥Õ´Õ¢Õ¥Ö€_Õ°Õ¸Õ¯Õ¿Õ¥Õ´Õ¢Õ¥Ö€_Õ¶Õ¸ÕµÕ¥Õ´Õ¢Õ¥Ö€_Õ¤Õ¥Õ¯Õ¿Õ¥Õ´Õ¢Õ¥Ö€".split("_"),accusative:"Õ°Õ¸Ö‚Õ¶Õ¾Õ¡Ö€Õ«_ÖƒÕ¥Õ¿Ö€Õ¾Õ¡Ö€Õ«_Õ´Õ¡Ö€Õ¿Õ«_Õ¡ÕºÖ€Õ«Õ¬Õ«_Õ´Õ¡ÕµÕ«Õ½Õ«_Õ°Õ¸Ö‚Õ¶Õ«Õ½Õ«_Õ°Õ¸Ö‚Õ¬Õ«Õ½Õ«_Ö…Õ£Õ¸Õ½Õ¿Õ¸Õ½Õ«_Õ½Õ¥ÕºÕ¿Õ¥Õ´Õ¢Õ¥Ö€Õ«_Õ°Õ¸Õ¯Õ¿Õ¥Õ´Õ¢Õ¥Ö€Õ«_Õ¶Õ¸ÕµÕ¥Õ´Õ¢Õ¥Ö€Õ«_Õ¤Õ¥Õ¯Õ¿Õ¥Õ´Õ¢Õ¥Ö€Õ«".split("_")},r=/D[oD]?(\[[^\[\]]*\]|\s+)+MMMM?/.test(t)?"accusative":"nominative";return i[r][n.month()]}function i(n){var t="Õ°Õ¶Õ¾_ÖƒÕ¿Ö€_Õ´Ö€Õ¿_Õ¡ÕºÖ€_Õ´ÕµÕ½_Õ°Õ¶Õ½_Õ°Õ¬Õ½_Ö…Õ£Õ½_Õ½ÕºÕ¿_Õ°Õ¯Õ¿_Õ¶Õ´Õ¢_Õ¤Õ¯Õ¿".split("_");return t[n.month()]}function r(n){var t="Õ¯Õ«Ö€Õ¡Õ¯Õ«_Õ¥Ö€Õ¯Õ¸Ö‚Õ·Õ¡Õ¢Õ©Õ«_Õ¥Ö€Õ¥Ö„Õ·Õ¡Õ¢Õ©Õ«_Õ¹Õ¸Ö€Õ¥Ö„Õ·Õ¡Õ¢Õ©Õ«_Õ°Õ«Õ¶Õ£Õ·Õ¡Õ¢Õ©Õ«_Õ¸Ö‚Ö€Õ¢Õ¡Õ©_Õ·Õ¡Õ¢Õ¡Õ©".split("_");return t[n.day()]}return n.lang("hy-am",{months:t,monthsShort:i,weekdays:r,weekdaysShort:"Õ¯Ö€Õ¯_Õ¥Ö€Õ¯_Õ¥Ö€Ö„_Õ¹Ö€Ö„_Õ°Õ¶Õ£_Õ¸Ö‚Ö€Õ¢_Õ·Õ¢Õ©".split("_"),weekdaysMin:"Õ¯Ö€Õ¯_Õ¥Ö€Õ¯_Õ¥Ö€Ö„_Õ¹Ö€Ö„_Õ°Õ¶Õ£_Õ¸Ö‚Ö€Õ¢_Õ·Õ¢Õ©".split("_"),longDateFormat:{LT:"HH:mm",L:"DD.MM.YYYY",LL:"D MMMM YYYY Õ©.",LLL:"D MMMM YYYY Õ©., LT",LLLL:"dddd, D MMMM YYYY Õ©., LT"},calendar:{sameDay:"[Õ¡ÕµÕ½Ö…Ö€] LT",nextDay:"[Õ¾Õ¡Õ²Õ¨] LT",lastDay:"[Õ¥Ö€Õ¥Õ¯] LT",nextWeek:function(){return"dddd [Ö…Ö€Õ¨ ÕªÕ¡Õ´Õ¨] LT"},lastWeek:function(){return"[Õ¡Õ¶ÖÕ¡Õ®] dddd [Ö…Ö€Õ¨ ÕªÕ¡Õ´Õ¨] LT"},sameElse:"L"},relativeTime:{future:"%s Õ°Õ¥Õ¿Õ¸",past:"%s Õ¡Õ¼Õ¡Õ»",s:"Õ´Õ« Ö„Õ¡Õ¶Õ« Õ¾Õ¡ÕµÖ€Õ¯ÕµÕ¡Õ¶",m:"Ö€Õ¸ÕºÕ¥",mm:"%d Ö€Õ¸ÕºÕ¥",h:"ÕªÕ¡Õ´",hh:"%d ÕªÕ¡Õ´",d:"Ö…Ö€",dd:"%d Ö…Ö€",M:"Õ¡Õ´Õ«Õ½",MM:"%d Õ¡Õ´Õ«Õ½",y:"Õ¿Õ¡Ö€Õ«",yy:"%d Õ¿Õ¡Ö€Õ«"},meridiem:function(n){return n<4?"Õ£Õ«Õ·Õ¥Ö€Õ¾Õ¡":n<12?"Õ¡Õ¼Õ¡Õ¾Õ¸Õ¿Õ¾Õ¡":n<17?"ÖÕ¥Ö€Õ¥Õ¯Õ¾Õ¡":"Õ¥Ö€Õ¥Õ¯Õ¸ÕµÕ¡Õ¶"},ordinal:function(n,t){switch(t){case"DDD":case"w":case"W":case"DDDo":return n===1?n+"-Õ«Õ¶":n+"-Ö€Õ¤";default:return n}},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("id",{months:"Januari_Februari_Maret_April_Mei_Juni_Juli_Agustus_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Ags_Sep_Okt_Nov_Des".split("_"),weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] LT",LLLL:"dddd, D MMMM YYYY [pukul] LT"},meridiem:function(n){return n<11?"pagi":n<15?"siang":n<19?"sore":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Besok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kemarin pukul] LT",lastWeek:"dddd [lalu pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){function i(n){return n%100==11?!0:n%10==1?!1:!0}function t(n,t,r,u){var f=n+" ";switch(r){case"s":return t||u?"nokkrar sekÃºndur":"nokkrum sekÃºndum";case"m":return t?"mÃ­nÃºta":"mÃ­nÃºtu";case"mm":return i(n)?f+(t||u?"mÃ­nÃºtur":"mÃ­nÃºtum"):t?f+"mÃ­nÃºta":f+"mÃ­nÃºtu";case"hh":return i(n)?f+(t||u?"klukkustundir":"klukkustundum"):f+"klukkustund";case"d":return t?"dagur":u?"dag":"degi";case"dd":return i(n)?t?f+"dagar":f+(u?"daga":"dÃ¶gum"):t?f+"dagur":f+(u?"dag":"degi");case"M":return t?"mÃ¡nuÃ°ur":u?"mÃ¡nuÃ°":"mÃ¡nuÃ°i";case"MM":return i(n)?t?f+"mÃ¡nuÃ°ir":f+(u?"mÃ¡nuÃ°i":"mÃ¡nuÃ°um"):t?f+"mÃ¡nuÃ°ur":f+(u?"mÃ¡nuÃ°":"mÃ¡nuÃ°i");case"y":return t||u?"Ã¡r":"Ã¡ri";case"yy":return i(n)?f+(t||u?"Ã¡r":"Ã¡rum"):f+(t||u?"Ã¡r":"Ã¡ri")}}return n.lang("is",{months:"janÃºar_febrÃºar_mars_aprÃ­l_maÃ­_jÃºnÃ­_jÃºlÃ­_Ã¡gÃºst_september_oktÃ³ber_nÃ³vember_desember".split("_"),monthsShort:"jan_feb_mar_apr_maÃ­_jÃºn_jÃºl_Ã¡gÃº_sep_okt_nÃ³v_des".split("_"),weekdays:"sunnudagur_mÃ¡nudagur_Ã¾riÃ°judagur_miÃ°vikudagur_fimmtudagur_fÃ¶studagur_laugardagur".split("_"),weekdaysShort:"sun_mÃ¡n_Ã¾ri_miÃ°_fim_fÃ¶s_lau".split("_"),weekdaysMin:"Su_MÃ¡_Ãžr_Mi_Fi_FÃ¶_La".split("_"),longDateFormat:{LT:"H:mm",L:"DD/MM/YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] LT",LLLL:"dddd, D. MMMM YYYY [kl.] LT"},calendar:{sameDay:"[Ã­ dag kl.] LT",nextDay:"[Ã¡ morgun kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[Ã­ gÃ¦r kl.] LT",lastWeek:"[sÃ­Ã°asta] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"eftir %s",past:"fyrir %s sÃ­Ã°an",s:t,m:t,mm:t,h:"klukkustund",hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("it",{months:"Gennaio_Febbraio_Marzo_Aprile_Maggio_Giugno_Luglio_Agosto_Settembre_Ottobre_Novembre_Dicembre".split("_"),monthsShort:"Gen_Feb_Mar_Apr_Mag_Giu_Lug_Ago_Set_Ott_Nov_Dic".split("_"),weekdays:"Domenica_LunedÃ¬_MartedÃ¬_MercoledÃ¬_GiovedÃ¬_VenerdÃ¬_Sabato".split("_"),weekdaysShort:"Dom_Lun_Mar_Mer_Gio_Ven_Sab".split("_"),weekdaysMin:"D_L_Ma_Me_G_V_S".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[Oggi alle] LT",nextDay:"[Domani alle] LT",nextWeek:"dddd [alle] LT",lastDay:"[Ieri alle] LT",lastWeek:"[lo scorso] dddd [alle] LT",sameElse:"L"},relativeTime:{future:function(n){return(/^[0-9].+$/.test(n)?"tra":"in")+" "+n},past:"%s fa",s:"alcuni secondi",m:"un minuto",mm:"%d minuti",h:"un'ora",hh:"%d ore",d:"un giorno",dd:"%d giorni",M:"un mese",MM:"%d mesi",y:"un anno",yy:"%d anni"},ordinal:"%dÂº",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("ja",{months:"1æœˆ_2æœˆ_3æœˆ_4æœˆ_5æœˆ_6æœˆ_7æœˆ_8æœˆ_9æœˆ_10æœˆ_11æœˆ_12æœˆ".split("_"),monthsShort:"1æœˆ_2æœˆ_3æœˆ_4æœˆ_5æœˆ_6æœˆ_7æœˆ_8æœˆ_9æœˆ_10æœˆ_11æœˆ_12æœˆ".split("_"),weekdays:"æ—¥æ›œæ—¥_æœˆæ›œæ—¥_ç«æ›œæ—¥_æ°´æ›œæ—¥_æœ¨æ›œæ—¥_é‡‘æ›œæ—¥_åœŸæ›œæ—¥".split("_"),weekdaysShort:"æ—¥_æœˆ_ç«_æ°´_æœ¨_é‡‘_åœŸ".split("_"),weekdaysMin:"æ—¥_æœˆ_ç«_æ°´_æœ¨_é‡‘_åœŸ".split("_"),longDateFormat:{LT:"Ahæ™‚måˆ†",L:"YYYY/MM/DD",LL:"YYYYå¹´MæœˆDæ—¥",LLL:"YYYYå¹´MæœˆDæ—¥LT",LLLL:"YYYYå¹´MæœˆDæ—¥LT dddd"},meridiem:function(n){return n<12?"åˆå‰":"åˆå¾Œ"},calendar:{sameDay:"[ä»Šæ—¥] LT",nextDay:"[æ˜Žæ—¥] LT",nextWeek:"[æ¥é€±]dddd LT",lastDay:"[æ˜¨æ—¥] LT",lastWeek:"[å‰é€±]dddd LT",sameElse:"L"},relativeTime:{future:"%så¾Œ",past:"%så‰",s:"æ•°ç§’",m:"1åˆ†",mm:"%dåˆ†",h:"1æ™‚é–“",hh:"%dæ™‚é–“",d:"1æ—¥",dd:"%dæ—¥",M:"1ãƒ¶æœˆ",MM:"%dãƒ¶æœˆ",y:"1å¹´",yy:"%då¹´"}})}),function(n){n(t)}(function(n){function t(n,t){var i={nominative:"áƒ˜áƒáƒœáƒ•áƒáƒ áƒ˜_áƒ—áƒ”áƒ‘áƒ”áƒ áƒ•áƒáƒšáƒ˜_áƒ›áƒáƒ áƒ¢áƒ˜_áƒáƒžáƒ áƒ˜áƒšáƒ˜_áƒ›áƒáƒ˜áƒ¡áƒ˜_áƒ˜áƒ•áƒœáƒ˜áƒ¡áƒ˜_áƒ˜áƒ•áƒšáƒ˜áƒ¡áƒ˜_áƒáƒ’áƒ•áƒ˜áƒ¡áƒ¢áƒ_áƒ¡áƒ”áƒ¥áƒ¢áƒ”áƒ›áƒ‘áƒ”áƒ áƒ˜_áƒáƒ¥áƒ¢áƒáƒ›áƒ‘áƒ”áƒ áƒ˜_áƒœáƒáƒ”áƒ›áƒ‘áƒ”áƒ áƒ˜_áƒ“áƒ”áƒ™áƒ”áƒ›áƒ‘áƒ”áƒ áƒ˜".split("_"),accusative:"áƒ˜áƒáƒœáƒ•áƒáƒ áƒ¡_áƒ—áƒ”áƒ‘áƒ”áƒ áƒ•áƒáƒšáƒ¡_áƒ›áƒáƒ áƒ¢áƒ¡_áƒáƒžáƒ áƒ˜áƒšáƒ˜áƒ¡_áƒ›áƒáƒ˜áƒ¡áƒ¡_áƒ˜áƒ•áƒœáƒ˜áƒ¡áƒ¡_áƒ˜áƒ•áƒšáƒ˜áƒ¡áƒ¡_áƒáƒ’áƒ•áƒ˜áƒ¡áƒ¢áƒ¡_áƒ¡áƒ”áƒ¥áƒ¢áƒ”áƒ›áƒ‘áƒ”áƒ áƒ¡_áƒáƒ¥áƒ¢áƒáƒ›áƒ‘áƒ”áƒ áƒ¡_áƒœáƒáƒ”áƒ›áƒ‘áƒ”áƒ áƒ¡_áƒ“áƒ”áƒ™áƒ”áƒ›áƒ‘áƒ”áƒ áƒ¡".split("_")},r=/D[oD] *MMMM?/.test(t)?"accusative":"nominative";return i[r][n.month()]}function i(n,t){var i={nominative:"áƒ™áƒ•áƒ˜áƒ áƒ_áƒáƒ áƒ¨áƒáƒ‘áƒáƒ—áƒ˜_áƒ¡áƒáƒ›áƒ¨áƒáƒ‘áƒáƒ—áƒ˜_áƒáƒ—áƒ®áƒ¨áƒáƒ‘áƒáƒ—áƒ˜_áƒ®áƒ£áƒ—áƒ¨áƒáƒ‘áƒáƒ—áƒ˜_áƒžáƒáƒ áƒáƒ¡áƒ™áƒ”áƒ•áƒ˜_áƒ¨áƒáƒ‘áƒáƒ—áƒ˜".split("_"),accusative:"áƒ™áƒ•áƒ˜áƒ áƒáƒ¡_áƒáƒ áƒ¨áƒáƒ‘áƒáƒ—áƒ¡_áƒ¡áƒáƒ›áƒ¨áƒáƒ‘áƒáƒ—áƒ¡_áƒáƒ—áƒ®áƒ¨áƒáƒ‘áƒáƒ—áƒ¡_áƒ®áƒ£áƒ—áƒ¨áƒáƒ‘áƒáƒ—áƒ¡_áƒžáƒáƒ áƒáƒ¡áƒ™áƒ”áƒ•áƒ¡_áƒ¨áƒáƒ‘áƒáƒ—áƒ¡".split("_")},r=/(áƒ¬áƒ˜áƒœáƒ|áƒ¨áƒ”áƒ›áƒ“áƒ”áƒ’)/.test(t)?"accusative":"nominative";return i[r][n.day()]}return n.lang("ka",{months:t,monthsShort:"áƒ˜áƒáƒœ_áƒ—áƒ”áƒ‘_áƒ›áƒáƒ _áƒáƒžáƒ _áƒ›áƒáƒ˜_áƒ˜áƒ•áƒœ_áƒ˜áƒ•áƒš_áƒáƒ’áƒ•_áƒ¡áƒ”áƒ¥_áƒáƒ¥áƒ¢_áƒœáƒáƒ”_áƒ“áƒ”áƒ™".split("_"),weekdays:i,weekdaysShort:"áƒ™áƒ•áƒ˜_áƒáƒ áƒ¨_áƒ¡áƒáƒ›_áƒáƒ—áƒ®_áƒ®áƒ£áƒ—_áƒžáƒáƒ _áƒ¨áƒáƒ‘".split("_"),weekdaysMin:"áƒ™áƒ•_áƒáƒ _áƒ¡áƒ_áƒáƒ—_áƒ®áƒ£_áƒžáƒ_áƒ¨áƒ".split("_"),longDateFormat:{LT:"h:mm A",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[áƒ“áƒ¦áƒ”áƒ¡] LT[-áƒ–áƒ”]",nextDay:"[áƒ®áƒ•áƒáƒš] LT[-áƒ–áƒ”]",lastDay:"[áƒ’áƒ£áƒ¨áƒ˜áƒœ] LT[-áƒ–áƒ”]",nextWeek:"[áƒ¨áƒ”áƒ›áƒ“áƒ”áƒ’] dddd LT[-áƒ–áƒ”]",lastWeek:"[áƒ¬áƒ˜áƒœáƒ] dddd LT-áƒ–áƒ”",sameElse:"L"},relativeTime:{future:function(n){return/(áƒ¬áƒáƒ›áƒ˜|áƒ¬áƒ£áƒ—áƒ˜|áƒ¡áƒáƒáƒ—áƒ˜|áƒ¬áƒ”áƒšáƒ˜)/.test(n)?n.replace(/áƒ˜$/,"áƒ¨áƒ˜"):n+"áƒ¨áƒ˜"},past:function(n){return/(áƒ¬áƒáƒ›áƒ˜|áƒ¬áƒ£áƒ—áƒ˜|áƒ¡áƒáƒáƒ—áƒ˜|áƒ“áƒ¦áƒ”|áƒ—áƒ•áƒ”)/.test(n)?n.replace(/(áƒ˜|áƒ”)$/,"áƒ˜áƒ¡ áƒ¬áƒ˜áƒœ"):/áƒ¬áƒ”áƒšáƒ˜/.test(n)?n.replace(/áƒ¬áƒ”áƒšáƒ˜$/,"áƒ¬áƒšáƒ˜áƒ¡ áƒ¬áƒ˜áƒœ"):void 0},s:"áƒ áƒáƒ›áƒ“áƒ”áƒœáƒ˜áƒ›áƒ” áƒ¬áƒáƒ›áƒ˜",m:"áƒ¬áƒ£áƒ—áƒ˜",mm:"%d áƒ¬áƒ£áƒ—áƒ˜",h:"áƒ¡áƒáƒáƒ—áƒ˜",hh:"%d áƒ¡áƒáƒáƒ—áƒ˜",d:"áƒ“áƒ¦áƒ”",dd:"%d áƒ“áƒ¦áƒ”",M:"áƒ—áƒ•áƒ”",MM:"%d áƒ—áƒ•áƒ”",y:"áƒ¬áƒ”áƒšáƒ˜",yy:"%d áƒ¬áƒ”áƒšáƒ˜"},ordinal:function(n){return n===0?n:n===1?n+"-áƒšáƒ˜":n<20||n<=100&&n%20==0||n%100==0?"áƒ›áƒ”-"+n:n+"-áƒ”"},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("km",{months:"áž˜áž€ážšáž¶_áž€áž»áž˜áŸ’áž—áŸˆ_áž˜áž·áž“áž¶_áž˜áŸážŸáž¶_áž§ážŸáž—áž¶_áž˜áž·ážáž»áž“áž¶_áž€áž€áŸ’áž€ážŠáž¶_ážŸáž¸áž áž¶_áž€áž‰áŸ’áž‰áž¶_ážáž»áž›áž¶_ážœáž·áž…áŸ’áž†áž·áž€áž¶_áž’áŸ’áž“áž¼".split("_"),monthsShort:"áž˜áž€ážšáž¶_áž€áž»áž˜áŸ’áž—áŸˆ_áž˜áž·áž“áž¶_áž˜áŸážŸáž¶_áž§ážŸáž—áž¶_áž˜áž·ážáž»áž“áž¶_áž€áž€áŸ’áž€ážŠáž¶_ážŸáž¸áž áž¶_áž€áž‰áŸ’áž‰áž¶_ážáž»áž›áž¶_ážœáž·áž…áŸ’áž†áž·áž€áž¶_áž’áŸ’áž“áž¼".split("_"),weekdays:"áž¢áž¶áž‘áž·ážáŸ’áž™_áž…áŸáž“áŸ’áž‘_áž¢áž„áŸ’áž‚áž¶ážš_áž–áž»áž’_áž–áŸ’ážšáž ážŸáŸ’áž”ážáž·áŸ_ážŸáž»áž€áŸ’ážš_ážŸáŸ…ážšáŸ".split("_"),weekdaysShort:"áž¢áž¶áž‘áž·ážáŸ’áž™_áž…áŸáž“áŸ’áž‘_áž¢áž„áŸ’áž‚áž¶ážš_áž–áž»áž’_áž–áŸ’ážšáž ážŸáŸ’áž”ážáž·áŸ_ážŸáž»áž€áŸ’ážš_ážŸáŸ…ážšáŸ".split("_"),weekdaysMin:"áž¢áž¶áž‘áž·ážáŸ’áž™_áž…áŸáž“áŸ’áž‘_áž¢áž„áŸ’áž‚áž¶ážš_áž–áž»áž’_áž–áŸ’ážšáž ážŸáŸ’áž”ážáž·áŸ_ážŸáž»áž€áŸ’ážš_ážŸáŸ…ážšáŸ".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[ážáŸ’áž„áŸƒáž“áŸˆ áž˜áŸ‰áŸ„áž„] LT",nextDay:"[ážŸáŸ’áž¢áŸ‚áž€ áž˜áŸ‰áŸ„áž„] LT",nextWeek:"dddd [áž˜áŸ‰áŸ„áž„] LT",lastDay:"[áž˜áŸ’ážŸáž·áž›áž˜áž·áž‰ áž˜áŸ‰áŸ„áž„] LT",lastWeek:"dddd [ážŸáž”áŸ’ážáž¶áž áŸáž˜áž»áž“] [áž˜áŸ‰áŸ„áž„] LT",sameElse:"L"},relativeTime:{future:"%sáž‘áŸ€áž",past:"%sáž˜áž»áž“",s:"áž”áŸ‰áž»áž“áŸ’áž˜áž¶áž“ážœáž·áž“áž¶áž‘áž¸",m:"áž˜áž½áž™áž“áž¶áž‘áž¸",mm:"%d áž“áž¶áž‘áž¸",h:"áž˜áž½áž™áž˜áŸ‰áŸ„áž„",hh:"%d áž˜áŸ‰áŸ„áž„",d:"áž˜áž½áž™ážáŸ’áž„áŸƒ",dd:"%d ážáŸ’áž„áŸƒ",M:"áž˜áž½áž™ážáŸ‚",MM:"%d ážáŸ‚",y:"áž˜áž½áž™áž†áŸ’áž“áž¶áŸ†",yy:"%d áž†áŸ’áž“áž¶áŸ†"},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("ko",{months:"1ì›”_2ì›”_3ì›”_4ì›”_5ì›”_6ì›”_7ì›”_8ì›”_9ì›”_10ì›”_11ì›”_12ì›”".split("_"),monthsShort:"1ì›”_2ì›”_3ì›”_4ì›”_5ì›”_6ì›”_7ì›”_8ì›”_9ì›”_10ì›”_11ì›”_12ì›”".split("_"),weekdays:"ì¼ìš”ì¼_ì›”ìš”ì¼_í™”ìš”ì¼_ìˆ˜ìš”ì¼_ëª©ìš”ì¼_ê¸ˆìš”ì¼_í† ìš”ì¼".split("_"),weekdaysShort:"ì¼_ì›”_í™”_ìˆ˜_ëª©_ê¸ˆ_í† ".split("_"),weekdaysMin:"ì¼_ì›”_í™”_ìˆ˜_ëª©_ê¸ˆ_í† ".split("_"),longDateFormat:{LT:"A hì‹œ mmë¶„",L:"YYYY.MM.DD",LL:"YYYYë…„ MMMM Dì¼",LLL:"YYYYë…„ MMMM Dì¼ LT",LLLL:"YYYYë…„ MMMM Dì¼ dddd LT"},meridiem:function(n){return n<12?"ì˜¤ì „":"ì˜¤í›„"},calendar:{sameDay:"ì˜¤ëŠ˜ LT",nextDay:"ë‚´ì¼ LT",nextWeek:"dddd LT",lastDay:"ì–´ì œ LT",lastWeek:"ì§€ë‚œì£¼ dddd LT",sameElse:"L"},relativeTime:{future:"%s í›„",past:"%s ì „",s:"ëª‡ì´ˆ",ss:"%dì´ˆ",m:"ì¼ë¶„",mm:"%dë¶„",h:"í•œì‹œê°„",hh:"%dì‹œê°„",d:"í•˜ë£¨",dd:"%dì¼",M:"í•œë‹¬",MM:"%dë‹¬",y:"ì¼ë…„",yy:"%dë…„"},ordinal:"%dì¼",meridiemParse:/(ì˜¤ì „|ì˜¤í›„)/,isPM:function(n){return n==="ì˜¤í›„"}})}),function(n){n(t)}(function(n){function t(n,t,i){var r={m:["eng Minutt","enger Minutt"],h:["eng Stonn","enger Stonn"],d:["een Dag","engem Dag"],dd:[n+" Deeg",n+" Deeg"],M:["ee Mount","engem Mount"],MM:[n+" MÃ©int",n+" MÃ©int"],y:["ee Joer","engem Joer"],yy:[n+" Joer",n+" Joer"]};return t?r[i][0]:r[i][1]}function r(n){var t=n.substr(0,n.indexOf(" "));return i(t)?"a "+n:"an "+n}function u(n){var t=n.substr(0,n.indexOf(" "));return i(t)?"viru "+n:"virun "+n}function f(){var n=this.format("d");return e(n)?"[Leschte] dddd [um] LT":"[Leschten] dddd [um] LT"}function e(n){n=parseInt(n,10);switch(n){case 0:case 1:case 3:case 5:case 6:return!0;default:return!1}}function i(n){if(n=parseInt(n,10),isNaN(n))return!1;if(n<0)return!0;if(n<10)return 4<=n&&n<=7?!0:!1;if(n<100){var t=n%10,r=n/10;return t===0?i(r):i(t)}if(n<1e4){while(n>=10)n=n/10;return i(n)}return n=n/1e3,i(n)}return n.lang("lb",{months:"Januar_Februar_MÃ¤erz_AbrÃ«ll_Mee_Juni_Juli_August_September_Oktober_November_Dezember".split("_"),monthsShort:"Jan._Febr._Mrz._Abr._Mee_Jun._Jul._Aug._Sept._Okt._Nov._Dez.".split("_"),weekdays:"Sonndeg_MÃ©indeg_DÃ«nschdeg_MÃ«ttwoch_Donneschdeg_Freideg_Samschdeg".split("_"),weekdaysShort:"So._MÃ©._DÃ«._MÃ«._Do._Fr._Sa.".split("_"),weekdaysMin:"So_MÃ©_DÃ«_MÃ«_Do_Fr_Sa".split("_"),longDateFormat:{LT:"H:mm [Auer]",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd, D. MMMM YYYY LT"},calendar:{sameDay:"[Haut um] LT",sameElse:"L",nextDay:"[Muer um] LT",nextWeek:"dddd [um] LT",lastDay:"[GÃ«schter um] LT",lastWeek:f},relativeTime:{future:r,past:u,s:"e puer Sekonnen",m:t,mm:"%d Minutten",h:t,hh:"%d Stonnen",d:t,dd:t,M:t,MM:t,y:t,yy:t},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){function o(n,t,i,r){return t?"kelios sekundÄ—s":r?"keliÅ³ sekundÅ¾iÅ³":"kelias sekundes"}function i(n,i,r,u){return i?t(r)[0]:u?t(r)[1]:t(r)[2]}function u(n){return n%10==0||n>10&&n<20}function t(n){return f[n].split("_")}function r(n,r,f,e){var o=n+" ";return n===1?o+i(n,r,f[0],e):r?o+(u(n)?t(f)[1]:t(f)[0]):e?o+t(f)[1]:o+(u(n)?t(f)[1]:t(f)[2])}function s(n,t){var r=t.indexOf("dddd HH:mm")===-1,i=e[n.weekday()];return r?i:i.substring(0,i.length-2)+"Ä¯"}var f={m:"minutÄ—_minutÄ—s_minutÄ™",mm:"minutÄ—s_minuÄiÅ³_minutes",h:"valanda_valandos_valandÄ…",hh:"valandos_valandÅ³_valandas",d:"diena_dienos_dienÄ…",dd:"dienos_dienÅ³_dienas",M:"mÄ—nuo_mÄ—nesio_mÄ—nesÄ¯",MM:"mÄ—nesiai_mÄ—nesiÅ³_mÄ—nesius",y:"metai_metÅ³_metus",yy:"metai_metÅ³_metus"},e="pirmadienis_antradienis_treÄiadienis_ketvirtadienis_penktadienis_Å¡eÅ¡tadienis_sekmadienis".split("_");return n.lang("lt",{months:"sausio_vasario_kovo_balandÅ¾io_geguÅ¾Ä—s_birÅ¾Ä—lio_liepos_rugpjÅ«Äio_rugsÄ—jo_spalio_lapkriÄio_gruodÅ¾io".split("_"),monthsShort:"sau_vas_kov_bal_geg_bir_lie_rgp_rgs_spa_lap_grd".split("_"),weekdays:s,weekdaysShort:"Sek_Pir_Ant_Tre_Ket_Pen_Å eÅ¡".split("_"),weekdaysMin:"S_P_A_T_K_Pn_Å ".split("_"),longDateFormat:{LT:"HH:mm",L:"YYYY-MM-DD",LL:"YYYY [m.] MMMM D [d.]",LLL:"YYYY [m.] MMMM D [d.], LT [val.]",LLLL:"YYYY [m.] MMMM D [d.], dddd, LT [val.]",l:"YYYY-MM-DD",ll:"YYYY [m.] MMMM D [d.]",lll:"YYYY [m.] MMMM D [d.], LT [val.]",llll:"YYYY [m.] MMMM D [d.], ddd, LT [val.]"},calendar:{sameDay:"[Å iandien] LT",nextDay:"[Rytoj] LT",nextWeek:"dddd LT",lastDay:"[Vakar] LT",lastWeek:"[PraÄ—jusÄ¯] dddd LT",sameElse:"L"},relativeTime:{future:"po %s",past:"prieÅ¡ %s",s:o,m:i,mm:r,h:i,hh:r,d:i,dd:r,M:i,MM:r,y:i,yy:r},ordinal:function(n){return n+"-oji"},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){function r(n,t,i){var r=n.split("_");return i?t%10==1&&t!==11?r[2]:r[3]:t%10==1&&t!==11?r[0]:r[1]}function t(n,t,u){return n+" "+r(i[u],n,t)}var i={mm:"minÅ«ti_minÅ«tes_minÅ«te_minÅ«tes",hh:"stundu_stundas_stunda_stundas",dd:"dienu_dienas_diena_dienas",MM:"mÄ“nesi_mÄ“neÅ¡us_mÄ“nesis_mÄ“neÅ¡i",yy:"gadu_gadus_gads_gadi"};return n.lang("lv",{months:"janvÄris_februÄris_marts_aprÄ«lis_maijs_jÅ«nijs_jÅ«lijs_augusts_septembris_oktobris_novembris_decembris".split("_"),monthsShort:"jan_feb_mar_apr_mai_jÅ«n_jÅ«l_aug_sep_okt_nov_dec".split("_"),weekdays:"svÄ“tdiena_pirmdiena_otrdiena_treÅ¡diena_ceturtdiena_piektdiena_sestdiena".split("_"),weekdaysShort:"Sv_P_O_T_C_Pk_S".split("_"),weekdaysMin:"Sv_P_O_T_C_Pk_S".split("_"),longDateFormat:{LT:"HH:mm",L:"DD.MM.YYYY",LL:"YYYY. [gada] D. MMMM",LLL:"YYYY. [gada] D. MMMM, LT",LLLL:"YYYY. [gada] D. MMMM, dddd, LT"},calendar:{sameDay:"[Å odien pulksten] LT",nextDay:"[RÄ«t pulksten] LT",nextWeek:"dddd [pulksten] LT",lastDay:"[Vakar pulksten] LT",lastWeek:"[PagÄjuÅ¡Ä] dddd [pulksten] LT",sameElse:"L"},relativeTime:{future:"%s vÄ“lÄk",past:"%s agrÄk",s:"daÅ¾as sekundes",m:"minÅ«ti",mm:t,h:"stundu",hh:t,d:"dienu",dd:t,M:"mÄ“nesi",MM:t,y:"gadu",yy:t},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("mk",{months:"Ñ˜Ð°Ð½ÑƒÐ°Ñ€Ð¸_Ñ„ÐµÐ²Ñ€ÑƒÐ°Ñ€Ð¸_Ð¼Ð°Ñ€Ñ‚_Ð°Ð¿Ñ€Ð¸Ð»_Ð¼Ð°Ñ˜_Ñ˜ÑƒÐ½Ð¸_Ñ˜ÑƒÐ»Ð¸_Ð°Ð²Ð³ÑƒÑÑ‚_ÑÐµÐ¿Ñ‚ÐµÐ¼Ð²Ñ€Ð¸_Ð¾ÐºÑ‚Ð¾Ð¼Ð²Ñ€Ð¸_Ð½Ð¾ÐµÐ¼Ð²Ñ€Ð¸_Ð´ÐµÐºÐµÐ¼Ð²Ñ€Ð¸".split("_"),monthsShort:"Ñ˜Ð°Ð½_Ñ„ÐµÐ²_Ð¼Ð°Ñ€_Ð°Ð¿Ñ€_Ð¼Ð°Ñ˜_Ñ˜ÑƒÐ½_Ñ˜ÑƒÐ»_Ð°Ð²Ð³_ÑÐµÐ¿_Ð¾ÐºÑ‚_Ð½Ð¾Ðµ_Ð´ÐµÐº".split("_"),weekdays:"Ð½ÐµÐ´ÐµÐ»Ð°_Ð¿Ð¾Ð½ÐµÐ´ÐµÐ»Ð½Ð¸Ðº_Ð²Ñ‚Ð¾Ñ€Ð½Ð¸Ðº_ÑÑ€ÐµÐ´Ð°_Ñ‡ÐµÑ‚Ð²Ñ€Ñ‚Ð¾Ðº_Ð¿ÐµÑ‚Ð¾Ðº_ÑÐ°Ð±Ð¾Ñ‚Ð°".split("_"),weekdaysShort:"Ð½ÐµÐ´_Ð¿Ð¾Ð½_Ð²Ñ‚Ð¾_ÑÑ€Ðµ_Ñ‡ÐµÑ‚_Ð¿ÐµÑ‚_ÑÐ°Ð±".split("_"),weekdaysMin:"Ð½e_Ð¿o_Ð²Ñ‚_ÑÑ€_Ñ‡Ðµ_Ð¿Ðµ_Ña".split("_"),longDateFormat:{LT:"H:mm",L:"D.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[Ð”ÐµÐ½ÐµÑ Ð²Ð¾] LT",nextDay:"[Ð£Ñ‚Ñ€Ðµ Ð²Ð¾] LT",nextWeek:"dddd [Ð²Ð¾] LT",lastDay:"[Ð’Ñ‡ÐµÑ€Ð° Ð²Ð¾] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[Ð’Ð¾ Ð¸Ð·Ð¼Ð¸Ð½Ð°Ñ‚Ð°Ñ‚Ð°] dddd [Ð²Ð¾] LT";case 1:case 2:case 4:case 5:return"[Ð’Ð¾ Ð¸Ð·Ð¼Ð¸Ð½Ð°Ñ‚Ð¸Ð¾Ñ‚] dddd [Ð²Ð¾] LT"}},sameElse:"L"},relativeTime:{future:"Ð¿Ð¾ÑÐ»Ðµ %s",past:"Ð¿Ñ€ÐµÐ´ %s",s:"Ð½ÐµÐºÐ¾Ð»ÐºÑƒ ÑÐµÐºÑƒÐ½Ð´Ð¸",m:"Ð¼Ð¸Ð½ÑƒÑ‚Ð°",mm:"%d Ð¼Ð¸Ð½ÑƒÑ‚Ð¸",h:"Ñ‡Ð°Ñ",hh:"%d Ñ‡Ð°ÑÐ°",d:"Ð´ÐµÐ½",dd:"%d Ð´ÐµÐ½Ð°",M:"Ð¼ÐµÑÐµÑ†",MM:"%d Ð¼ÐµÑÐµÑ†Ð¸",y:"Ð³Ð¾Ð´Ð¸Ð½Ð°",yy:"%d Ð³Ð¾Ð´Ð¸Ð½Ð¸"},ordinal:function(n){var t=n%10,i=n%100;return n===0?n+"-ÐµÐ²":i===0?n+"-ÐµÐ½":i>10&&i<20?n+"-Ñ‚Ð¸":t===1?n+"-Ð²Ð¸":t===2?n+"-Ñ€Ð¸":t===7||t===8?n+"-Ð¼Ð¸":n+"-Ñ‚Ð¸"},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("ml",{months:"à´œà´¨àµà´µà´°à´¿_à´«àµ†à´¬àµà´°àµà´µà´°à´¿_à´®à´¾àµ¼à´šàµà´šàµ_à´à´ªàµà´°à´¿àµ½_à´®àµ‡à´¯àµ_à´œàµ‚àµº_à´œàµ‚à´²àµˆ_à´“à´—à´¸àµà´±àµà´±àµ_à´¸àµ†à´ªàµà´±àµà´±à´‚à´¬àµ¼_à´’à´•àµà´Ÿàµ‹à´¬àµ¼_à´¨à´µà´‚à´¬àµ¼_à´¡à´¿à´¸à´‚à´¬àµ¼".split("_"),monthsShort:"à´œà´¨àµ._à´«àµ†à´¬àµà´°àµ._à´®à´¾àµ¼._à´à´ªàµà´°à´¿._à´®àµ‡à´¯àµ_à´œàµ‚àµº_à´œàµ‚à´²àµˆ._à´“à´—._à´¸àµ†à´ªàµà´±àµà´±._à´’à´•àµà´Ÿàµ‹._à´¨à´µà´‚._à´¡à´¿à´¸à´‚.".split("_"),weekdays:"à´žà´¾à´¯à´±à´¾à´´àµà´š_à´¤à´¿à´™àµà´•à´³à´¾à´´àµà´š_à´šàµŠà´µàµà´µà´¾à´´àµà´š_à´¬àµà´§à´¨à´¾à´´àµà´š_à´µàµà´¯à´¾à´´à´¾à´´àµà´š_à´µàµ†à´³àµà´³à´¿à´¯à´¾à´´àµà´š_à´¶à´¨à´¿à´¯à´¾à´´àµà´š".split("_"),weekdaysShort:"à´žà´¾à´¯àµ¼_à´¤à´¿à´™àµà´•àµ¾_à´šàµŠà´µàµà´µ_à´¬àµà´§àµ»_à´µàµà´¯à´¾à´´à´‚_à´µàµ†à´³àµà´³à´¿_à´¶à´¨à´¿".split("_"),weekdaysMin:"à´žà´¾_à´¤à´¿_à´šàµŠ_à´¬àµ_à´µàµà´¯à´¾_à´µàµ†_à´¶".split("_"),longDateFormat:{LT:"A h:mm -à´¨àµ",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, LT",LLLL:"dddd, D MMMM YYYY, LT"},calendar:{sameDay:"[à´‡à´¨àµà´¨àµ] LT",nextDay:"[à´¨à´¾à´³àµ†] LT",nextWeek:"dddd, LT",lastDay:"[à´‡à´¨àµà´¨à´²àµ†] LT",lastWeek:"[à´•à´´à´¿à´žàµà´ž] dddd, LT",sameElse:"L"},relativeTime:{future:"%s à´•à´´à´¿à´žàµà´žàµ",past:"%s à´®àµàµ»à´ªàµ",s:"à´…àµ½à´ª à´¨à´¿à´®à´¿à´·à´™àµà´™àµ¾",m:"à´’à´°àµ à´®à´¿à´¨à´¿à´±àµà´±àµ",mm:"%d à´®à´¿à´¨à´¿à´±àµà´±àµ",h:"à´’à´°àµ à´®à´£à´¿à´•àµà´•àµ‚àµ¼",hh:"%d à´®à´£à´¿à´•àµà´•àµ‚àµ¼",d:"à´’à´°àµ à´¦à´¿à´µà´¸à´‚",dd:"%d à´¦à´¿à´µà´¸à´‚",M:"à´’à´°àµ à´®à´¾à´¸à´‚",MM:"%d à´®à´¾à´¸à´‚",y:"à´’à´°àµ à´µàµ¼à´·à´‚",yy:"%d à´µàµ¼à´·à´‚"},meridiem:function(n){return n<4?"à´°à´¾à´¤àµà´°à´¿":n<12?"à´°à´¾à´µà´¿à´²àµ†":n<17?"à´‰à´šàµà´š à´•à´´à´¿à´žàµà´žàµ":n<20?"à´µàµˆà´•àµà´¨àµà´¨àµ‡à´°à´‚":"à´°à´¾à´¤àµà´°à´¿"}})}),function(n){n(t)}(function(n){var t={"1":"à¥§","2":"à¥¨","3":"à¥©","4":"à¥ª","5":"à¥«","6":"à¥¬","7":"à¥­","8":"à¥®","9":"à¥¯","0":"à¥¦"},i={"à¥§":"1","à¥¨":"2","à¥©":"3","à¥ª":"4","à¥«":"5","à¥¬":"6","à¥­":"7","à¥®":"8","à¥¯":"9","à¥¦":"0"};return n.lang("mr",{months:"à¤œà¤¾à¤¨à¥‡à¤µà¤¾à¤°à¥€_à¤«à¥‡à¤¬à¥à¤°à¥à¤µà¤¾à¤°à¥€_à¤®à¤¾à¤°à¥à¤š_à¤à¤ªà¥à¤°à¤¿à¤²_à¤®à¥‡_à¤œà¥‚à¤¨_à¤œà¥à¤²à¥ˆ_à¤‘à¤—à¤¸à¥à¤Ÿ_à¤¸à¤ªà¥à¤Ÿà¥‡à¤‚à¤¬à¤°_à¤‘à¤•à¥à¤Ÿà¥‹à¤¬à¤°_à¤¨à¥‹à¤µà¥à¤¹à¥‡à¤‚à¤¬à¤°_à¤¡à¤¿à¤¸à¥‡à¤‚à¤¬à¤°".split("_"),monthsShort:"à¤œà¤¾à¤¨à¥‡._à¤«à¥‡à¤¬à¥à¤°à¥._à¤®à¤¾à¤°à¥à¤š._à¤à¤ªà¥à¤°à¤¿._à¤®à¥‡._à¤œà¥‚à¤¨._à¤œà¥à¤²à¥ˆ._à¤‘à¤—._à¤¸à¤ªà¥à¤Ÿà¥‡à¤‚._à¤‘à¤•à¥à¤Ÿà¥‹._à¤¨à¥‹à¤µà¥à¤¹à¥‡à¤‚._à¤¡à¤¿à¤¸à¥‡à¤‚.".split("_"),weekdays:"à¤°à¤µà¤¿à¤µà¤¾à¤°_à¤¸à¥‹à¤®à¤µà¤¾à¤°_à¤®à¤‚à¤—à¤³à¤µà¤¾à¤°_à¤¬à¥à¤§à¤µà¤¾à¤°_à¤—à¥à¤°à¥‚à¤µà¤¾à¤°_à¤¶à¥à¤•à¥à¤°à¤µà¤¾à¤°_à¤¶à¤¨à¤¿à¤µà¤¾à¤°".split("_"),weekdaysShort:"à¤°à¤µà¤¿_à¤¸à¥‹à¤®_à¤®à¤‚à¤—à¤³_à¤¬à¥à¤§_à¤—à¥à¤°à¥‚_à¤¶à¥à¤•à¥à¤°_à¤¶à¤¨à¤¿".split("_"),weekdaysMin:"à¤°_à¤¸à¥‹_à¤®à¤‚_à¤¬à¥_à¤—à¥_à¤¶à¥_à¤¶".split("_"),longDateFormat:{LT:"A h:mm à¤µà¤¾à¤œà¤¤à¤¾",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, LT",LLLL:"dddd, D MMMM YYYY, LT"},calendar:{sameDay:"[à¤†à¤œ] LT",nextDay:"[à¤‰à¤¦à¥à¤¯à¤¾] LT",nextWeek:"dddd, LT",lastDay:"[à¤•à¤¾à¤²] LT",lastWeek:"[à¤®à¤¾à¤—à¥€à¤²] dddd, LT",sameElse:"L"},relativeTime:{future:"%s à¤¨à¤‚à¤¤à¤°",past:"%s à¤ªà¥‚à¤°à¥à¤µà¥€",s:"à¤¸à¥‡à¤•à¤‚à¤¦",m:"à¤à¤• à¤®à¤¿à¤¨à¤¿à¤Ÿ",mm:"%d à¤®à¤¿à¤¨à¤¿à¤Ÿà¥‡",h:"à¤à¤• à¤¤à¤¾à¤¸",hh:"%d à¤¤à¤¾à¤¸",d:"à¤à¤• à¤¦à¤¿à¤µà¤¸",dd:"%d à¤¦à¤¿à¤µà¤¸",M:"à¤à¤• à¤®à¤¹à¤¿à¤¨à¤¾",MM:"%d à¤®à¤¹à¤¿à¤¨à¥‡",y:"à¤à¤• à¤µà¤°à¥à¤·",yy:"%d à¤µà¤°à¥à¤·à¥‡"},preparse:function(n){return n.replace(/[à¥§à¥¨à¥©à¥ªà¥«à¥¬à¥­à¥®à¥¯à¥¦]/g,function(n){return i[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return t[n]})},meridiem:function(n){return n<4?"à¤°à¤¾à¤¤à¥à¤°à¥€":n<10?"à¤¸à¤•à¤¾à¤³à¥€":n<17?"à¤¦à¥à¤ªà¤¾à¤°à¥€":n<20?"à¤¸à¤¾à¤¯à¤‚à¤•à¤¾à¤³à¥€":"à¤°à¤¾à¤¤à¥à¤°à¥€"},week:{dow:0,doy:6}})}),function(n){n(t)}(function(n){return n.lang("ms-my",{months:"Januari_Februari_Mac_April_Mei_Jun_Julai_Ogos_September_Oktober_November_Disember".split("_"),monthsShort:"Jan_Feb_Mac_Apr_Mei_Jun_Jul_Ogs_Sep_Okt_Nov_Dis".split("_"),weekdays:"Ahad_Isnin_Selasa_Rabu_Khamis_Jumaat_Sabtu".split("_"),weekdaysShort:"Ahd_Isn_Sel_Rab_Kha_Jum_Sab".split("_"),weekdaysMin:"Ah_Is_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] LT",LLLL:"dddd, D MMMM YYYY [pukul] LT"},meridiem:function(n){return n<11?"pagi":n<15?"tengahari":n<19?"petang":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Esok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kelmarin pukul] LT",lastWeek:"dddd [lepas pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lepas",s:"beberapa saat",m:"seminit",mm:"%d minit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("nb",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan._feb._mars_april_mai_juni_juli_aug._sep._okt._nov._des.".split("_"),weekdays:"sÃ¸ndag_mandag_tirsdag_onsdag_torsdag_fredag_lÃ¸rdag".split("_"),weekdaysShort:"sÃ¸._ma._ti._on._to._fr._lÃ¸.".split("_"),weekdaysMin:"sÃ¸_ma_ti_on_to_fr_lÃ¸".split("_"),longDateFormat:{LT:"H.mm",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY [kl.] LT",LLLL:"dddd D. MMMM YYYY [kl.] LT"},calendar:{sameDay:"[i dag kl.] LT",nextDay:"[i morgen kl.] LT",nextWeek:"dddd [kl.] LT",lastDay:"[i gÃ¥r kl.] LT",lastWeek:"[forrige] dddd [kl.] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"for %s siden",s:"noen sekunder",m:"ett minutt",mm:"%d minutter",h:"en time",hh:"%d timer",d:"en dag",dd:"%d dager",M:"en mÃ¥ned",MM:"%d mÃ¥neder",y:"ett Ã¥r",yy:"%d Ã¥r"},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){var t={"1":"à¥§","2":"à¥¨","3":"à¥©","4":"à¥ª","5":"à¥«","6":"à¥¬","7":"à¥­","8":"à¥®","9":"à¥¯","0":"à¥¦"},i={"à¥§":"1","à¥¨":"2","à¥©":"3","à¥ª":"4","à¥«":"5","à¥¬":"6","à¥­":"7","à¥®":"8","à¥¯":"9","à¥¦":"0"};return n.lang("ne",{months:"à¤œà¤¨à¤µà¤°à¥€_à¤«à¥‡à¤¬à¥à¤°à¥à¤µà¤°à¥€_à¤®à¤¾à¤°à¥à¤š_à¤…à¤ªà¥à¤°à¤¿à¤²_à¤®à¤ˆ_à¤œà¥à¤¨_à¤œà¥à¤²à¤¾à¤ˆ_à¤…à¤—à¤·à¥à¤Ÿ_à¤¸à¥‡à¤ªà¥à¤Ÿà¥‡à¤®à¥à¤¬à¤°_à¤…à¤•à¥à¤Ÿà¥‹à¤¬à¤°_à¤¨à¥‹à¤­à¥‡à¤®à¥à¤¬à¤°_à¤¡à¤¿à¤¸à¥‡à¤®à¥à¤¬à¤°".split("_"),monthsShort:"à¤œà¤¨._à¤«à¥‡à¤¬à¥à¤°à¥._à¤®à¤¾à¤°à¥à¤š_à¤…à¤ªà¥à¤°à¤¿._à¤®à¤ˆ_à¤œà¥à¤¨_à¤œà¥à¤²à¤¾à¤ˆ._à¤…à¤—._à¤¸à¥‡à¤ªà¥à¤Ÿ._à¤…à¤•à¥à¤Ÿà¥‹._à¤¨à¥‹à¤­à¥‡._à¤¡à¤¿à¤¸à¥‡.".split("_"),weekdays:"à¤†à¤‡à¤¤à¤¬à¤¾à¤°_à¤¸à¥‹à¤®à¤¬à¤¾à¤°_à¤®à¤™à¥à¤—à¤²à¤¬à¤¾à¤°_à¤¬à¥à¤§à¤¬à¤¾à¤°_à¤¬à¤¿à¤¹à¤¿à¤¬à¤¾à¤°_à¤¶à¥à¤•à¥à¤°à¤¬à¤¾à¤°_à¤¶à¤¨à¤¿à¤¬à¤¾à¤°".split("_"),weekdaysShort:"à¤†à¤‡à¤¤._à¤¸à¥‹à¤®._à¤®à¤™à¥à¤—à¤²._à¤¬à¥à¤§._à¤¬à¤¿à¤¹à¤¿._à¤¶à¥à¤•à¥à¤°._à¤¶à¤¨à¤¿.".split("_"),weekdaysMin:"à¤†à¤‡._à¤¸à¥‹._à¤®à¤™à¥_à¤¬à¥._à¤¬à¤¿._à¤¶à¥._à¤¶.".split("_"),longDateFormat:{LT:"Aà¤•à¥‹ h:mm à¤¬à¤œà¥‡",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, LT",LLLL:"dddd, D MMMM YYYY, LT"},preparse:function(n){return n.replace(/[à¥§à¥¨à¥©à¥ªà¥«à¥¬à¥­à¥®à¥¯à¥¦]/g,function(n){return i[n]})},postformat:function(n){return n.replace(/\d/g,function(n){return t[n]})},meridiem:function(n){return n<3?"à¤°à¤¾à¤¤à¥€":n<10?"à¤¬à¤¿à¤¹à¤¾à¤¨":n<15?"à¤¦à¤¿à¤‰à¤à¤¸à¥‹":n<18?"à¤¬à¥‡à¤²à¥à¤•à¤¾":n<20?"à¤¸à¤¾à¤à¤":"à¤°à¤¾à¤¤à¥€"},calendar:{sameDay:"[à¤†à¤œ] LT",nextDay:"[à¤­à¥‹à¤²à¥€] LT",nextWeek:"[à¤†à¤‰à¤à¤¦à¥‹] dddd[,] LT",lastDay:"[à¤¹à¤¿à¤œà¥‹] LT",lastWeek:"[à¤—à¤à¤•à¥‹] dddd[,] LT",sameElse:"L"},relativeTime:{future:"%sà¤®à¤¾",past:"%s à¤…à¤—à¤¾à¤¡à¥€",s:"à¤•à¥‡à¤¹à¥€ à¤¸à¤®à¤¯",m:"à¤à¤• à¤®à¤¿à¤¨à¥‡à¤Ÿ",mm:"%d à¤®à¤¿à¤¨à¥‡à¤Ÿ",h:"à¤à¤• à¤˜à¤£à¥à¤Ÿà¤¾",hh:"%d à¤˜à¤£à¥à¤Ÿà¤¾",d:"à¤à¤• à¤¦à¤¿à¤¨",dd:"%d à¤¦à¤¿à¤¨",M:"à¤à¤• à¤®à¤¹à¤¿à¤¨à¤¾",MM:"%d à¤®à¤¹à¤¿à¤¨à¤¾",y:"à¤à¤• à¤¬à¤°à¥à¤·",yy:"%d à¤¬à¤°à¥à¤·"},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){var t="jan._feb._mrt._apr._mei_jun._jul._aug._sep._okt._nov._dec.".split("_"),i="jan_feb_mrt_apr_mei_jun_jul_aug_sep_okt_nov_dec".split("_");return n.lang("nl",{months:"januari_februari_maart_april_mei_juni_juli_augustus_september_oktober_november_december".split("_"),monthsShort:function(n,r){return/-MMM-/.test(r)?i[n.month()]:t[n.month()]},weekdays:"zondag_maandag_dinsdag_woensdag_donderdag_vrijdag_zaterdag".split("_"),weekdaysShort:"zo._ma._di._wo._do._vr._za.".split("_"),weekdaysMin:"Zo_Ma_Di_Wo_Do_Vr_Za".split("_"),longDateFormat:{LT:"HH:mm",L:"DD-MM-YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:"[vandaag om] LT",nextDay:"[morgen om] LT",nextWeek:"dddd [om] LT",lastDay:"[gisteren om] LT",lastWeek:"[afgelopen] dddd [om] LT",sameElse:"L"},relativeTime:{future:"over %s",past:"%s geleden",s:"een paar seconden",m:"Ã©Ã©n minuut",mm:"%d minuten",h:"Ã©Ã©n uur",hh:"%d uur",d:"Ã©Ã©n dag",dd:"%d dagen",M:"Ã©Ã©n maand",MM:"%d maanden",y:"Ã©Ã©n jaar",yy:"%d jaar"},ordinal:function(n){return n+(n===1||n===8||n>=20?"ste":"de")},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("nn",{months:"januar_februar_mars_april_mai_juni_juli_august_september_oktober_november_desember".split("_"),monthsShort:"jan_feb_mar_apr_mai_jun_jul_aug_sep_okt_nov_des".split("_"),weekdays:"sundag_mÃ¥ndag_tysdag_onsdag_torsdag_fredag_laurdag".split("_"),weekdaysShort:"sun_mÃ¥n_tys_ons_tor_fre_lau".split("_"),weekdaysMin:"su_mÃ¥_ty_on_to_fr_lÃ¸".split("_"),longDateFormat:{LT:"HH:mm",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:"[I dag klokka] LT",nextDay:"[I morgon klokka] LT",nextWeek:"dddd [klokka] LT",lastDay:"[I gÃ¥r klokka] LT",lastWeek:"[FÃ¸regÃ¥ande] dddd [klokka] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"for %s sidan",s:"nokre sekund",m:"eit minutt",mm:"%d minutt",h:"ein time",hh:"%d timar",d:"ein dag",dd:"%d dagar",M:"ein mÃ¥nad",MM:"%d mÃ¥nader",y:"eit Ã¥r",yy:"%d Ã¥r"},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){function i(n){return n%10<5&&n%10>1&&~~(n/10)%10!=1}function t(n,t,r){var u=n+" ";switch(r){case"m":return t?"minuta":"minutÄ™";case"mm":return u+(i(n)?"minuty":"minut");case"h":return t?"godzina":"godzinÄ™";case"hh":return u+(i(n)?"godziny":"godzin");case"MM":return u+(i(n)?"miesiÄ…ce":"miesiÄ™cy");case"yy":return u+(i(n)?"lata":"lat")}}var r="styczeÅ„_luty_marzec_kwiecieÅ„_maj_czerwiec_lipiec_sierpieÅ„_wrzesieÅ„_paÅºdziernik_listopad_grudzieÅ„".split("_"),u="stycznia_lutego_marca_kwietnia_maja_czerwca_lipca_sierpnia_wrzeÅ›nia_paÅºdziernika_listopada_grudnia".split("_");return n.lang("pl",{months:function(n,t){return/D MMMM/.test(t)?u[n.month()]:r[n.month()]},monthsShort:"sty_lut_mar_kwi_maj_cze_lip_sie_wrz_paÅº_lis_gru".split("_"),weekdays:"niedziela_poniedziaÅ‚ek_wtorek_Å›roda_czwartek_piÄ…tek_sobota".split("_"),weekdaysShort:"nie_pon_wt_Å›r_czw_pt_sb".split("_"),weekdaysMin:"N_Pn_Wt_Åšr_Cz_Pt_So".split("_"),longDateFormat:{LT:"HH:mm",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[DziÅ› o] LT",nextDay:"[Jutro o] LT",nextWeek:"[W] dddd [o] LT",lastDay:"[Wczoraj o] LT",lastWeek:function(){switch(this.day()){case 0:return"[W zeszÅ‚Ä… niedzielÄ™ o] LT";case 3:return"[W zeszÅ‚Ä… Å›rodÄ™ o] LT";case 6:return"[W zeszÅ‚Ä… sobotÄ™ o] LT";default:return"[W zeszÅ‚y] dddd [o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"%s temu",s:"kilka sekund",m:t,mm:t,h:t,hh:t,d:"1 dzieÅ„",dd:"%d dni",M:"miesiÄ…c",MM:t,y:"rok",yy:t},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("pt-br",{months:"janeiro_fevereiro_marÃ§o_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"domingo_segunda-feira_terÃ§a-feira_quarta-feira_quinta-feira_sexta-feira_sÃ¡bado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_sÃ¡b".split("_"),weekdaysMin:"dom_2Âª_3Âª_4Âª_5Âª_6Âª_sÃ¡b".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY [Ã s] LT",LLLL:"dddd, D [de] MMMM [de] YYYY [Ã s] LT"},calendar:{sameDay:"[Hoje Ã s] LT",nextDay:"[AmanhÃ£ Ã s] LT",nextWeek:"dddd [Ã s] LT",lastDay:"[Ontem Ã s] LT",lastWeek:function(){return this.day()===0||this.day()===6?"[Ãšltimo] dddd [Ã s] LT":"[Ãšltima] dddd [Ã s] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"%s atrÃ¡s",s:"segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mÃªs",MM:"%d meses",y:"um ano",yy:"%d anos"},ordinal:"%dÂº"})}),function(n){n(t)}(function(n){return n.lang("pt",{months:"janeiro_fevereiro_marÃ§o_abril_maio_junho_julho_agosto_setembro_outubro_novembro_dezembro".split("_"),monthsShort:"jan_fev_mar_abr_mai_jun_jul_ago_set_out_nov_dez".split("_"),weekdays:"domingo_segunda-feira_terÃ§a-feira_quarta-feira_quinta-feira_sexta-feira_sÃ¡bado".split("_"),weekdaysShort:"dom_seg_ter_qua_qui_sex_sÃ¡b".split("_"),weekdaysMin:"dom_2Âª_3Âª_4Âª_5Âª_6Âª_sÃ¡b".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D [de] MMMM [de] YYYY",LLL:"D [de] MMMM [de] YYYY LT",LLLL:"dddd, D [de] MMMM [de] YYYY LT"},calendar:{sameDay:"[Hoje Ã s] LT",nextDay:"[AmanhÃ£ Ã s] LT",nextWeek:"dddd [Ã s] LT",lastDay:"[Ontem Ã s] LT",lastWeek:function(){return this.day()===0||this.day()===6?"[Ãšltimo] dddd [Ã s] LT":"[Ãšltima] dddd [Ã s] LT"},sameElse:"L"},relativeTime:{future:"em %s",past:"%s atrÃ¡s",s:"segundos",m:"um minuto",mm:"%d minutos",h:"uma hora",hh:"%d horas",d:"um dia",dd:"%d dias",M:"um mÃªs",MM:"%d meses",y:"um ano",yy:"%d anos"},ordinal:"%dÂº",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){function t(n,t,i){var r=" ";return(n%100>=20||n>=100&&n%100==0)&&(r=" de "),n+r+{mm:"minute",hh:"ore",dd:"zile",MM:"luni",yy:"ani"}[i]}return n.lang("ro",{months:"ianuarie_februarie_martie_aprilie_mai_iunie_iulie_august_septembrie_octombrie_noiembrie_decembrie".split("_"),monthsShort:"ian._febr._mart._apr._mai_iun._iul._aug._sept._oct._nov._dec.".split("_"),weekdays:"duminicÄƒ_luni_marÈ›i_miercuri_joi_vineri_sÃ¢mbÄƒtÄƒ".split("_"),weekdaysShort:"Dum_Lun_Mar_Mie_Joi_Vin_SÃ¢m".split("_"),weekdaysMin:"Du_Lu_Ma_Mi_Jo_Vi_SÃ¢".split("_"),longDateFormat:{LT:"H:mm",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY H:mm",LLLL:"dddd, D MMMM YYYY H:mm"},calendar:{sameDay:"[azi la] LT",nextDay:"[mÃ¢ine la] LT",nextWeek:"dddd [la] LT",lastDay:"[ieri la] LT",lastWeek:"[fosta] dddd [la] LT",sameElse:"L"},relativeTime:{future:"peste %s",past:"%s Ã®n urmÄƒ",s:"cÃ¢teva secunde",m:"un minut",mm:t,h:"o orÄƒ",hh:t,d:"o zi",dd:t,M:"o lunÄƒ",MM:t,y:"un an",yy:t},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){function i(n,t){var i=n.split("_");return t%10==1&&t%100!=11?i[0]:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?i[1]:i[2]}function t(n,t,r){var u={mm:t?"Ð¼Ð¸Ð½ÑƒÑ‚Ð°_Ð¼Ð¸Ð½ÑƒÑ‚Ñ‹_Ð¼Ð¸Ð½ÑƒÑ‚":"Ð¼Ð¸Ð½ÑƒÑ‚Ñƒ_Ð¼Ð¸Ð½ÑƒÑ‚Ñ‹_Ð¼Ð¸Ð½ÑƒÑ‚",hh:"Ñ‡Ð°Ñ_Ñ‡Ð°ÑÐ°_Ñ‡Ð°ÑÐ¾Ð²",dd:"Ð´ÐµÐ½ÑŒ_Ð´Ð½Ñ_Ð´Ð½ÐµÐ¹",MM:"Ð¼ÐµÑÑÑ†_Ð¼ÐµÑÑÑ†Ð°_Ð¼ÐµÑÑÑ†ÐµÐ²",yy:"Ð³Ð¾Ð´_Ð³Ð¾Ð´Ð°_Ð»ÐµÑ‚"};return r==="m"?t?"Ð¼Ð¸Ð½ÑƒÑ‚Ð°":"Ð¼Ð¸Ð½ÑƒÑ‚Ñƒ":n+" "+i(u[r],+n)}function r(n,t){var i={nominative:"ÑÐ½Ð²Ð°Ñ€ÑŒ_Ñ„ÐµÐ²Ñ€Ð°Ð»ÑŒ_Ð¼Ð°Ñ€Ñ‚_Ð°Ð¿Ñ€ÐµÐ»ÑŒ_Ð¼Ð°Ð¹_Ð¸ÑŽÐ½ÑŒ_Ð¸ÑŽÐ»ÑŒ_Ð°Ð²Ð³ÑƒÑÑ‚_ÑÐµÐ½Ñ‚ÑÐ±Ñ€ÑŒ_Ð¾ÐºÑ‚ÑÐ±Ñ€ÑŒ_Ð½Ð¾ÑÐ±Ñ€ÑŒ_Ð´ÐµÐºÐ°Ð±Ñ€ÑŒ".split("_"),accusative:"ÑÐ½Ð²Ð°Ñ€Ñ_Ñ„ÐµÐ²Ñ€Ð°Ð»Ñ_Ð¼Ð°Ñ€Ñ‚Ð°_Ð°Ð¿Ñ€ÐµÐ»Ñ_Ð¼Ð°Ñ_Ð¸ÑŽÐ½Ñ_Ð¸ÑŽÐ»Ñ_Ð°Ð²Ð³ÑƒÑÑ‚Ð°_ÑÐµÐ½Ñ‚ÑÐ±Ñ€Ñ_Ð¾ÐºÑ‚ÑÐ±Ñ€Ñ_Ð½Ð¾ÑÐ±Ñ€Ñ_Ð´ÐµÐºÐ°Ð±Ñ€Ñ".split("_")},r=/D[oD]?(\[[^\[\]]*\]|\s+)+MMMM?/.test(t)?"accusative":"nominative";return i[r][n.month()]}function u(n,t){var i={nominative:"ÑÐ½Ð²_Ñ„ÐµÐ²_Ð¼Ð°Ñ€_Ð°Ð¿Ñ€_Ð¼Ð°Ð¹_Ð¸ÑŽÐ½ÑŒ_Ð¸ÑŽÐ»ÑŒ_Ð°Ð²Ð³_ÑÐµÐ½_Ð¾ÐºÑ‚_Ð½Ð¾Ñ_Ð´ÐµÐº".split("_"),accusative:"ÑÐ½Ð²_Ñ„ÐµÐ²_Ð¼Ð°Ñ€_Ð°Ð¿Ñ€_Ð¼Ð°Ñ_Ð¸ÑŽÐ½Ñ_Ð¸ÑŽÐ»Ñ_Ð°Ð²Ð³_ÑÐµÐ½_Ð¾ÐºÑ‚_Ð½Ð¾Ñ_Ð´ÐµÐº".split("_")},r=/D[oD]?(\[[^\[\]]*\]|\s+)+MMMM?/.test(t)?"accusative":"nominative";return i[r][n.month()]}function f(n,t){var i={nominative:"Ð²Ð¾ÑÐºÑ€ÐµÑÐµÐ½ÑŒÐµ_Ð¿Ð¾Ð½ÐµÐ´ÐµÐ»ÑŒÐ½Ð¸Ðº_Ð²Ñ‚Ð¾Ñ€Ð½Ð¸Ðº_ÑÑ€ÐµÐ´Ð°_Ñ‡ÐµÑ‚Ð²ÐµÑ€Ð³_Ð¿ÑÑ‚Ð½Ð¸Ñ†Ð°_ÑÑƒÐ±Ð±Ð¾Ñ‚Ð°".split("_"),accusative:"Ð²Ð¾ÑÐºÑ€ÐµÑÐµÐ½ÑŒÐµ_Ð¿Ð¾Ð½ÐµÐ´ÐµÐ»ÑŒÐ½Ð¸Ðº_Ð²Ñ‚Ð¾Ñ€Ð½Ð¸Ðº_ÑÑ€ÐµÐ´Ñƒ_Ñ‡ÐµÑ‚Ð²ÐµÑ€Ð³_Ð¿ÑÑ‚Ð½Ð¸Ñ†Ñƒ_ÑÑƒÐ±Ð±Ð¾Ñ‚Ñƒ".split("_")},r=/\[ ?[Ð’Ð²] ?(?:Ð¿Ñ€Ð¾ÑˆÐ»ÑƒÑŽ|ÑÐ»ÐµÐ´ÑƒÑŽÑ‰ÑƒÑŽ)? ?\] ?dddd/.test(t)?"accusative":"nominative";return i[r][n.day()]}return n.lang("ru",{months:r,monthsShort:u,weekdays:f,weekdaysShort:"Ð²Ñ_Ð¿Ð½_Ð²Ñ‚_ÑÑ€_Ñ‡Ñ‚_Ð¿Ñ‚_ÑÐ±".split("_"),weekdaysMin:"Ð²Ñ_Ð¿Ð½_Ð²Ñ‚_ÑÑ€_Ñ‡Ñ‚_Ð¿Ñ‚_ÑÐ±".split("_"),monthsParse:[/^ÑÐ½Ð²/i,/^Ñ„ÐµÐ²/i,/^Ð¼Ð°Ñ€/i,/^Ð°Ð¿Ñ€/i,/^Ð¼Ð°[Ð¹|Ñ]/i,/^Ð¸ÑŽÐ½/i,/^Ð¸ÑŽÐ»/i,/^Ð°Ð²Ð³/i,/^ÑÐµÐ½/i,/^Ð¾ÐºÑ‚/i,/^Ð½Ð¾Ñ/i,/^Ð´ÐµÐº/i],longDateFormat:{LT:"HH:mm",L:"DD.MM.YYYY",LL:"D MMMM YYYY Ð³.",LLL:"D MMMM YYYY Ð³., LT",LLLL:"dddd, D MMMM YYYY Ð³., LT"},calendar:{sameDay:"[Ð¡ÐµÐ³Ð¾Ð´Ð½Ñ Ð²] LT",nextDay:"[Ð—Ð°Ð²Ñ‚Ñ€Ð° Ð²] LT",lastDay:"[Ð’Ñ‡ÐµÑ€Ð° Ð²] LT",nextWeek:function(){return this.day()===2?"[Ð’Ð¾] dddd [Ð²] LT":"[Ð’] dddd [Ð²] LT"},lastWeek:function(){switch(this.day()){case 0:return"[Ð’ Ð¿Ñ€Ð¾ÑˆÐ»Ð¾Ðµ] dddd [Ð²] LT";case 1:case 2:case 4:return"[Ð’ Ð¿Ñ€Ð¾ÑˆÐ»Ñ‹Ð¹] dddd [Ð²] LT";case 3:case 5:case 6:return"[Ð’ Ð¿Ñ€Ð¾ÑˆÐ»ÑƒÑŽ] dddd [Ð²] LT"}},sameElse:"L"},relativeTime:{future:"Ñ‡ÐµÑ€ÐµÐ· %s",past:"%s Ð½Ð°Ð·Ð°Ð´",s:"Ð½ÐµÑÐºÐ¾Ð»ÑŒÐºÐ¾ ÑÐµÐºÑƒÐ½Ð´",m:t,mm:t,h:"Ñ‡Ð°Ñ",hh:t,d:"Ð´ÐµÐ½ÑŒ",dd:t,M:"Ð¼ÐµÑÑÑ†",MM:t,y:"Ð³Ð¾Ð´",yy:t},meridiem:function(n){return n<4?"Ð½Ð¾Ñ‡Ð¸":n<12?"ÑƒÑ‚Ñ€Ð°":n<17?"Ð´Ð½Ñ":"Ð²ÐµÑ‡ÐµÑ€Ð°"},ordinal:function(n,t){switch(t){case"M":case"d":case"DDD":return n+"-Ð¹";case"D":return n+"-Ð³Ð¾";case"w":case"W":return n+"-Ñ";default:return n}},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){function i(n){return n>1&&n<5}function t(n,t,r,u){var f=n+" ";switch(r){case"s":return t||u?"pÃ¡r sekÃºnd":"pÃ¡r sekundami";case"m":return t?"minÃºta":u?"minÃºtu":"minÃºtou";case"mm":return t||u?f+(i(n)?"minÃºty":"minÃºt"):f+"minÃºtami";case"h":return t?"hodina":u?"hodinu":"hodinou";case"hh":return t||u?f+(i(n)?"hodiny":"hodÃ­n"):f+"hodinami";case"d":return t||u?"deÅˆ":"dÅˆom";case"dd":return t||u?f+(i(n)?"dni":"dnÃ­"):f+"dÅˆami";case"M":return t||u?"mesiac":"mesiacom";case"MM":return t||u?f+(i(n)?"mesiace":"mesiacov"):f+"mesiacmi";case"y":return t||u?"rok":"rokom";case"yy":return t||u?f+(i(n)?"roky":"rokov"):f+"rokmi"}}var r="januÃ¡r_februÃ¡r_marec_aprÃ­l_mÃ¡j_jÃºn_jÃºl_august_september_oktÃ³ber_november_december".split("_"),u="jan_feb_mar_apr_mÃ¡j_jÃºn_jÃºl_aug_sep_okt_nov_dec".split("_");return n.lang("sk",{months:r,monthsShort:u,monthsParse:function(n,t){for(var r=[],i=0;i<12;i++)r[i]=new RegExp("^"+n[i]+"$|^"+t[i]+"$","i");return r}(r,u),weekdays:"nedeÄ¾a_pondelok_utorok_streda_Å¡tvrtok_piatok_sobota".split("_"),weekdaysShort:"ne_po_ut_st_Å¡t_pi_so".split("_"),weekdaysMin:"ne_po_ut_st_Å¡t_pi_so".split("_"),longDateFormat:{LT:"H:mm",L:"DD.MM.YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd D. MMMM YYYY LT"},calendar:{sameDay:"[dnes o] LT",nextDay:"[zajtra o] LT",nextWeek:function(){switch(this.day()){case 0:return"[v nedeÄ¾u o] LT";case 1:case 2:return"[v] dddd [o] LT";case 3:return"[v stredu o] LT";case 4:return"[vo Å¡tvrtok o] LT";case 5:return"[v piatok o] LT";case 6:return"[v sobotu o] LT"}},lastDay:"[vÄera o] LT",lastWeek:function(){switch(this.day()){case 0:return"[minulÃº nedeÄ¾u o] LT";case 1:case 2:return"[minulÃ½] dddd [o] LT";case 3:return"[minulÃº stredu o] LT";case 4:case 5:return"[minulÃ½] dddd [o] LT";case 6:return"[minulÃº sobotu o] LT"}},sameElse:"L"},relativeTime:{future:"za %s",past:"pred %s",s:t,m:t,mm:t,h:t,hh:t,d:t,dd:t,M:t,MM:t,y:t,yy:t},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){function t(n,t,i){var r=n+" ";switch(i){case"m":return t?"ena minuta":"eno minuto";case"mm":return r+(n===1?"minuta":n===2?"minuti":n===3||n===4?"minute":"minut");case"h":return t?"ena ura":"eno uro";case"hh":return r+(n===1?"ura":n===2?"uri":n===3||n===4?"ure":"ur");case"dd":return r+(n===1?"dan":"dni");case"MM":return r+(n===1?"mesec":n===2?"meseca":n===3||n===4?"mesece":"mesecev");case"yy":return r+(n===1?"leto":n===2?"leti":n===3||n===4?"leta":"let")}}return n.lang("sl",{months:"januar_februar_marec_april_maj_junij_julij_avgust_september_oktober_november_december".split("_"),monthsShort:"jan._feb._mar._apr._maj._jun._jul._avg._sep._okt._nov._dec.".split("_"),weekdays:"nedelja_ponedeljek_torek_sreda_Äetrtek_petek_sobota".split("_"),weekdaysShort:"ned._pon._tor._sre._Äet._pet._sob.".split("_"),weekdaysMin:"ne_po_to_sr_Äe_pe_so".split("_"),longDateFormat:{LT:"H:mm",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd, D. MMMM YYYY LT"},calendar:{sameDay:"[danes ob] LT",nextDay:"[jutri ob] LT",nextWeek:function(){switch(this.day()){case 0:return"[v] [nedeljo] [ob] LT";case 3:return"[v] [sredo] [ob] LT";case 6:return"[v] [soboto] [ob] LT";case 1:case 2:case 4:case 5:return"[v] dddd [ob] LT"}},lastDay:"[vÄeraj ob] LT",lastWeek:function(){switch(this.day()){case 0:case 3:case 6:return"[prejÅ¡nja] dddd [ob] LT";case 1:case 2:case 4:case 5:return"[prejÅ¡nji] dddd [ob] LT"}},sameElse:"L"},relativeTime:{future:"Äez %s",past:"%s nazaj",s:"nekaj sekund",m:t,mm:t,h:t,hh:t,d:"en dan",dd:t,M:"en mesec",MM:t,y:"eno leto",yy:t},ordinal:"%d.",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("sq",{months:"Janar_Shkurt_Mars_Prill_Maj_Qershor_Korrik_Gusht_Shtator_Tetor_NÃ«ntor_Dhjetor".split("_"),monthsShort:"Jan_Shk_Mar_Pri_Maj_Qer_Kor_Gus_Sht_Tet_NÃ«n_Dhj".split("_"),weekdays:"E Diel_E HÃ«nÃ«_E MartÃ«_E MÃ«rkurÃ«_E Enjte_E Premte_E ShtunÃ«".split("_"),weekdaysShort:"Die_HÃ«n_Mar_MÃ«r_Enj_Pre_Sht".split("_"),weekdaysMin:"D_H_Ma_MÃ«_E_P_Sh".split("_"),meridiem:function(n){return n<12?"PD":"MD"},longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[Sot nÃ«] LT",nextDay:"[NesÃ«r nÃ«] LT",nextWeek:"dddd [nÃ«] LT",lastDay:"[Dje nÃ«] LT",lastWeek:"dddd [e kaluar nÃ«] LT",sameElse:"L"},relativeTime:{future:"nÃ« %s",past:"%s mÃ« parÃ«",s:"disa sekonda",m:"njÃ« minutÃ«",mm:"%d minuta",h:"njÃ« orÃ«",hh:"%d orÃ«",d:"njÃ« ditÃ«",dd:"%d ditÃ«",M:"njÃ« muaj",MM:"%d muaj",y:"njÃ« vit",yy:"%d vite"},ordinal:"%d.",week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){var t={words:{m:["Ñ˜ÐµÐ´Ð°Ð½ Ð¼Ð¸Ð½ÑƒÑ‚","Ñ˜ÐµÐ´Ð½Ðµ Ð¼Ð¸Ð½ÑƒÑ‚Ðµ"],mm:["Ð¼Ð¸Ð½ÑƒÑ‚","Ð¼Ð¸Ð½ÑƒÑ‚Ðµ","Ð¼Ð¸Ð½ÑƒÑ‚Ð°"],h:["Ñ˜ÐµÐ´Ð°Ð½ ÑÐ°Ñ‚","Ñ˜ÐµÐ´Ð½Ð¾Ð³ ÑÐ°Ñ‚Ð°"],hh:["ÑÐ°Ñ‚","ÑÐ°Ñ‚Ð°","ÑÐ°Ñ‚Ð¸"],dd:["Ð´Ð°Ð½","Ð´Ð°Ð½Ð°","Ð´Ð°Ð½Ð°"],MM:["Ð¼ÐµÑÐµÑ†","Ð¼ÐµÑÐµÑ†Ð°","Ð¼ÐµÑÐµÑ†Ð¸"],yy:["Ð³Ð¾Ð´Ð¸Ð½Ð°","Ð³Ð¾Ð´Ð¸Ð½Ðµ","Ð³Ð¾Ð´Ð¸Ð½Ð°"]},correctGrammaticalCase:function(n,t){return n===1?t[0]:n>=2&&n<=4?t[1]:t[2]},translate:function(n,i,r){var u=t.words[r];return r.length===1?i?u[0]:u[1]:n+" "+t.correctGrammaticalCase(n,u)}};return n.lang("sr-cyr",{months:["Ñ˜Ð°Ð½ÑƒÐ°Ñ€","Ñ„ÐµÐ±Ñ€ÑƒÐ°Ñ€","Ð¼Ð°Ñ€Ñ‚","Ð°Ð¿Ñ€Ð¸Ð»","Ð¼Ð°Ñ˜","Ñ˜ÑƒÐ½","Ñ˜ÑƒÐ»","Ð°Ð²Ð³ÑƒÑÑ‚","ÑÐµÐ¿Ñ‚ÐµÐ¼Ð±Ð°Ñ€","Ð¾ÐºÑ‚Ð¾Ð±Ð°Ñ€","Ð½Ð¾Ð²ÐµÐ¼Ð±Ð°Ñ€","Ð´ÐµÑ†ÐµÐ¼Ð±Ð°Ñ€"],monthsShort:["Ñ˜Ð°Ð½.","Ñ„ÐµÐ±.","Ð¼Ð°Ñ€.","Ð°Ð¿Ñ€.","Ð¼Ð°Ñ˜","Ñ˜ÑƒÐ½","Ñ˜ÑƒÐ»","Ð°Ð²Ð³.","ÑÐµÐ¿.","Ð¾ÐºÑ‚.","Ð½Ð¾Ð².","Ð´ÐµÑ†."],weekdays:["Ð½ÐµÐ´ÐµÑ™Ð°","Ð¿Ð¾Ð½ÐµÐ´ÐµÑ™Ð°Ðº","ÑƒÑ‚Ð¾Ñ€Ð°Ðº","ÑÑ€ÐµÐ´Ð°","Ñ‡ÐµÑ‚Ð²Ñ€Ñ‚Ð°Ðº","Ð¿ÐµÑ‚Ð°Ðº","ÑÑƒÐ±Ð¾Ñ‚Ð°"],weekdaysShort:["Ð½ÐµÐ´.","Ð¿Ð¾Ð½.","ÑƒÑ‚Ð¾.","ÑÑ€Ðµ.","Ñ‡ÐµÑ‚.","Ð¿ÐµÑ‚.","ÑÑƒÐ±."],weekdaysMin:["Ð½Ðµ","Ð¿Ð¾","ÑƒÑ‚","ÑÑ€","Ñ‡Ðµ","Ð¿Ðµ","ÑÑƒ"],longDateFormat:{LT:"H:mm",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd, D. MMMM YYYY LT"},calendar:{sameDay:"[Ð´Ð°Ð½Ð°Ñ Ñƒ] LT",nextDay:"[ÑÑƒÑ‚Ñ€Ð° Ñƒ] LT",nextWeek:function(){switch(this.day()){case 0:return"[Ñƒ] [Ð½ÐµÐ´ÐµÑ™Ñƒ] [Ñƒ] LT";case 3:return"[Ñƒ] [ÑÑ€ÐµÐ´Ñƒ] [Ñƒ] LT";case 6:return"[Ñƒ] [ÑÑƒÐ±Ð¾Ñ‚Ñƒ] [Ñƒ] LT";case 1:case 2:case 4:case 5:return"[Ñƒ] dddd [Ñƒ] LT"}},lastDay:"[Ñ˜ÑƒÑ‡Ðµ Ñƒ] LT",lastWeek:function(){return["[Ð¿Ñ€Ð¾ÑˆÐ»Ðµ] [Ð½ÐµÐ´ÐµÑ™Ðµ] [Ñƒ] LT","[Ð¿Ñ€Ð¾ÑˆÐ»Ð¾Ð³] [Ð¿Ð¾Ð½ÐµÐ´ÐµÑ™ÐºÐ°] [Ñƒ] LT","[Ð¿Ñ€Ð¾ÑˆÐ»Ð¾Ð³] [ÑƒÑ‚Ð¾Ñ€ÐºÐ°] [Ñƒ] LT","[Ð¿Ñ€Ð¾ÑˆÐ»Ðµ] [ÑÑ€ÐµÐ´Ðµ] [Ñƒ] LT","[Ð¿Ñ€Ð¾ÑˆÐ»Ð¾Ð³] [Ñ‡ÐµÑ‚Ð²Ñ€Ñ‚ÐºÐ°] [Ñƒ] LT","[Ð¿Ñ€Ð¾ÑˆÐ»Ð¾Ð³] [Ð¿ÐµÑ‚ÐºÐ°] [Ñƒ] LT","[Ð¿Ñ€Ð¾ÑˆÐ»Ðµ] [ÑÑƒÐ±Ð¾Ñ‚Ðµ] [Ñƒ] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"Ð·Ð° %s",past:"Ð¿Ñ€Ðµ %s",s:"Ð½ÐµÐºÐ¾Ð»Ð¸ÐºÐ¾ ÑÐµÐºÑƒÐ½Ð´Ð¸",m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:"Ð´Ð°Ð½",dd:t.translate,M:"Ð¼ÐµÑÐµÑ†",MM:t.translate,y:"Ð³Ð¾Ð´Ð¸Ð½Ñƒ",yy:t.translate},ordinal:"%d.",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){var t={words:{m:["jedan minut","jedne minute"],mm:["minut","minute","minuta"],h:["jedan sat","jednog sata"],hh:["sat","sata","sati"],dd:["dan","dana","dana"],MM:["mesec","meseca","meseci"],yy:["godina","godine","godina"]},correctGrammaticalCase:function(n,t){return n===1?t[0]:n>=2&&n<=4?t[1]:t[2]},translate:function(n,i,r){var u=t.words[r];return r.length===1?i?u[0]:u[1]:n+" "+t.correctGrammaticalCase(n,u)}};return n.lang("sr",{months:["januar","februar","mart","april","maj","jun","jul","avgust","septembar","oktobar","novembar","decembar"],monthsShort:["jan.","feb.","mar.","apr.","maj","jun","jul","avg.","sep.","okt.","nov.","dec."],weekdays:["nedelja","ponedeljak","utorak","sreda","Äetvrtak","petak","subota"],weekdaysShort:["ned.","pon.","uto.","sre.","Äet.","pet.","sub."],weekdaysMin:["ne","po","ut","sr","Äe","pe","su"],longDateFormat:{LT:"H:mm",L:"DD. MM. YYYY",LL:"D. MMMM YYYY",LLL:"D. MMMM YYYY LT",LLLL:"dddd, D. MMMM YYYY LT"},calendar:{sameDay:"[danas u] LT",nextDay:"[sutra u] LT",nextWeek:function(){switch(this.day()){case 0:return"[u] [nedelju] [u] LT";case 3:return"[u] [sredu] [u] LT";case 6:return"[u] [subotu] [u] LT";case 1:case 2:case 4:case 5:return"[u] dddd [u] LT"}},lastDay:"[juÄe u] LT",lastWeek:function(){return["[proÅ¡le] [nedelje] [u] LT","[proÅ¡log] [ponedeljka] [u] LT","[proÅ¡log] [utorka] [u] LT","[proÅ¡le] [srede] [u] LT","[proÅ¡log] [Äetvrtka] [u] LT","[proÅ¡log] [petka] [u] LT","[proÅ¡le] [subote] [u] LT"][this.day()]},sameElse:"L"},relativeTime:{future:"za %s",past:"pre %s",s:"nekoliko sekundi",m:t.translate,mm:t.translate,h:t.translate,hh:t.translate,d:"dan",dd:t.translate,M:"mesec",MM:t.translate,y:"godinu",yy:t.translate},ordinal:"%d.",week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("sv",{months:"januari_februari_mars_april_maj_juni_juli_augusti_september_oktober_november_december".split("_"),monthsShort:"jan_feb_mar_apr_maj_jun_jul_aug_sep_okt_nov_dec".split("_"),weekdays:"sÃ¶ndag_mÃ¥ndag_tisdag_onsdag_torsdag_fredag_lÃ¶rdag".split("_"),weekdaysShort:"sÃ¶n_mÃ¥n_tis_ons_tor_fre_lÃ¶r".split("_"),weekdaysMin:"sÃ¶_mÃ¥_ti_on_to_fr_lÃ¶".split("_"),longDateFormat:{LT:"HH:mm",L:"YYYY-MM-DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:"[Idag] LT",nextDay:"[Imorgon] LT",lastDay:"[IgÃ¥r] LT",nextWeek:"dddd LT",lastWeek:"[FÃ¶rra] dddd[en] LT",sameElse:"L"},relativeTime:{future:"om %s",past:"fÃ¶r %s sedan",s:"nÃ¥gra sekunder",m:"en minut",mm:"%d minuter",h:"en timme",hh:"%d timmar",d:"en dag",dd:"%d dagar",M:"en mÃ¥nad",MM:"%d mÃ¥nader",y:"ett Ã¥r",yy:"%d Ã¥r"},ordinal:function(n){var t=n%10,i=~~(n%100/10)==1?"e":t===1?"a":t===2?"a":t===3?"e":"e";return n+i},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("ta",{months:"à®œà®©à®µà®°à®¿_à®ªà®¿à®ªà¯à®°à®µà®°à®¿_à®®à®¾à®°à¯à®šà¯_à®à®ªà¯à®°à®²à¯_à®®à¯‡_à®œà¯‚à®©à¯_à®œà¯‚à®²à¯ˆ_à®†à®•à®¸à¯à®Ÿà¯_à®šà¯†à®ªà¯à®Ÿà¯†à®®à¯à®ªà®°à¯_à®…à®•à¯à®Ÿà¯‡à®¾à®ªà®°à¯_à®¨à®µà®®à¯à®ªà®°à¯_à®Ÿà®¿à®šà®®à¯à®ªà®°à¯".split("_"),monthsShort:"à®œà®©à®µà®°à®¿_à®ªà®¿à®ªà¯à®°à®µà®°à®¿_à®®à®¾à®°à¯à®šà¯_à®à®ªà¯à®°à®²à¯_à®®à¯‡_à®œà¯‚à®©à¯_à®œà¯‚à®²à¯ˆ_à®†à®•à®¸à¯à®Ÿà¯_à®šà¯†à®ªà¯à®Ÿà¯†à®®à¯à®ªà®°à¯_à®…à®•à¯à®Ÿà¯‡à®¾à®ªà®°à¯_à®¨à®µà®®à¯à®ªà®°à¯_à®Ÿà®¿à®šà®®à¯à®ªà®°à¯".split("_"),weekdays:"à®žà®¾à®¯à®¿à®±à¯à®±à¯à®•à¯à®•à®¿à®´à®®à¯ˆ_à®¤à®¿à®™à¯à®•à®Ÿà¯à®•à®¿à®´à®®à¯ˆ_à®šà¯†à®µà¯à®µà®¾à®¯à¯à®•à®¿à®´à®®à¯ˆ_à®ªà¯à®¤à®©à¯à®•à®¿à®´à®®à¯ˆ_à®µà®¿à®¯à®¾à®´à®•à¯à®•à®¿à®´à®®à¯ˆ_à®µà¯†à®³à¯à®³à®¿à®•à¯à®•à®¿à®´à®®à¯ˆ_à®šà®©à®¿à®•à¯à®•à®¿à®´à®®à¯ˆ".split("_"),weekdaysShort:"à®žà®¾à®¯à®¿à®±à¯_à®¤à®¿à®™à¯à®•à®³à¯_à®šà¯†à®µà¯à®µà®¾à®¯à¯_à®ªà¯à®¤à®©à¯_à®µà®¿à®¯à®¾à®´à®©à¯_à®µà¯†à®³à¯à®³à®¿_à®šà®©à®¿".split("_"),weekdaysMin:"à®žà®¾_à®¤à®¿_à®šà¯†_à®ªà¯_à®µà®¿_à®µà¯†_à®š".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY, LT",LLLL:"dddd, D MMMM YYYY, LT"},calendar:{sameDay:"[à®‡à®©à¯à®±à¯] LT",nextDay:"[à®¨à®¾à®³à¯ˆ] LT",nextWeek:"dddd, LT",lastDay:"[à®¨à¯‡à®±à¯à®±à¯] LT",lastWeek:"[à®•à®Ÿà®¨à¯à®¤ à®µà®¾à®°à®®à¯] dddd, LT",sameElse:"L"},relativeTime:{future:"%s à®‡à®²à¯",past:"%s à®®à¯à®©à¯",s:"à®’à®°à¯ à®šà®¿à®² à®µà®¿à®¨à®¾à®Ÿà®¿à®•à®³à¯",m:"à®’à®°à¯ à®¨à®¿à®®à®¿à®Ÿà®®à¯",mm:"%d à®¨à®¿à®®à®¿à®Ÿà®™à¯à®•à®³à¯",h:"à®’à®°à¯ à®®à®£à®¿ à®¨à¯‡à®°à®®à¯",hh:"%d à®®à®£à®¿ à®¨à¯‡à®°à®®à¯",d:"à®’à®°à¯ à®¨à®¾à®³à¯",dd:"%d à®¨à®¾à®Ÿà¯à®•à®³à¯",M:"à®’à®°à¯ à®®à®¾à®¤à®®à¯",MM:"%d à®®à®¾à®¤à®™à¯à®•à®³à¯",y:"à®’à®°à¯ à®µà®°à¯à®Ÿà®®à¯",yy:"%d à®†à®£à¯à®Ÿà¯à®•à®³à¯"},ordinal:function(n){return n+"à®µà®¤à¯"},meridiem:function(n){return n>=6&&n<=10?" à®•à®¾à®²à¯ˆ":n>=10&&n<=14?" à®¨à®£à¯à®ªà®•à®²à¯":n>=14&&n<=18?" à®Žà®±à¯à®ªà®¾à®Ÿà¯":n>=18&&n<=20?" à®®à®¾à®²à¯ˆ":n>=20&&n<=24?" à®‡à®°à®µà¯":n>=0&&n<=6?" à®µà¯ˆà®•à®±à¯ˆ":void 0},week:{dow:0,doy:6}})}),function(n){n(t)}(function(n){return n.lang("th",{months:"à¸¡à¸à¸£à¸²à¸„à¸¡_à¸à¸¸à¸¡à¸ à¸²à¸žà¸±à¸™à¸˜à¹Œ_à¸¡à¸µà¸™à¸²à¸„à¸¡_à¹€à¸¡à¸©à¸²à¸¢à¸™_à¸žà¸¤à¸©à¸ à¸²à¸„à¸¡_à¸¡à¸´à¸–à¸¸à¸™à¸²à¸¢à¸™_à¸à¸£à¸à¸Žà¸²à¸„à¸¡_à¸ªà¸´à¸‡à¸«à¸²à¸„à¸¡_à¸à¸±à¸™à¸¢à¸²à¸¢à¸™_à¸•à¸¸à¸¥à¸²à¸„à¸¡_à¸žà¸¤à¸¨à¸ˆà¸´à¸à¸²à¸¢à¸™_à¸˜à¸±à¸™à¸§à¸²à¸„à¸¡".split("_"),monthsShort:"à¸¡à¸à¸£à¸²_à¸à¸¸à¸¡à¸ à¸²_à¸¡à¸µà¸™à¸²_à¹€à¸¡à¸©à¸²_à¸žà¸¤à¸©à¸ à¸²_à¸¡à¸´à¸–à¸¸à¸™à¸²_à¸à¸£à¸à¸Žà¸²_à¸ªà¸´à¸‡à¸«à¸²_à¸à¸±à¸™à¸¢à¸²_à¸•à¸¸à¸¥à¸²_à¸žà¸¤à¸¨à¸ˆà¸´à¸à¸²_à¸˜à¸±à¸™à¸§à¸²".split("_"),weekdays:"à¸­à¸²à¸—à¸´à¸•à¸¢à¹Œ_à¸ˆà¸±à¸™à¸—à¸£à¹Œ_à¸­à¸±à¸‡à¸„à¸²à¸£_à¸žà¸¸à¸˜_à¸žà¸¤à¸«à¸±à¸ªà¸šà¸”à¸µ_à¸¨à¸¸à¸à¸£à¹Œ_à¹€à¸ªà¸²à¸£à¹Œ".split("_"),weekdaysShort:"à¸­à¸²à¸—à¸´à¸•à¸¢à¹Œ_à¸ˆà¸±à¸™à¸—à¸£à¹Œ_à¸­à¸±à¸‡à¸„à¸²à¸£_à¸žà¸¸à¸˜_à¸žà¸¤à¸«à¸±à¸ª_à¸¨à¸¸à¸à¸£à¹Œ_à¹€à¸ªà¸²à¸£à¹Œ".split("_"),weekdaysMin:"à¸­à¸²._à¸ˆ._à¸­._à¸ž._à¸žà¸¤._à¸¨._à¸ª.".split("_"),longDateFormat:{LT:"H à¸™à¸²à¸¬à¸´à¸à¸² m à¸™à¸²à¸—à¸µ",L:"YYYY/MM/DD",LL:"D MMMM YYYY",LLL:"D MMMM YYYY à¹€à¸§à¸¥à¸² LT",LLLL:"à¸§à¸±à¸™ddddà¸—à¸µà¹ˆ D MMMM YYYY à¹€à¸§à¸¥à¸² LT"},meridiem:function(n){return n<12?"à¸à¹ˆà¸­à¸™à¹€à¸—à¸µà¹ˆà¸¢à¸‡":"à¸«à¸¥à¸±à¸‡à¹€à¸—à¸µà¹ˆà¸¢à¸‡"},calendar:{sameDay:"[à¸§à¸±à¸™à¸™à¸µà¹‰ à¹€à¸§à¸¥à¸²] LT",nextDay:"[à¸žà¸£à¸¸à¹ˆà¸‡à¸™à¸µà¹‰ à¹€à¸§à¸¥à¸²] LT",nextWeek:"dddd[à¸«à¸™à¹‰à¸² à¹€à¸§à¸¥à¸²] LT",lastDay:"[à¹€à¸¡à¸·à¹ˆà¸­à¸§à¸²à¸™à¸™à¸µà¹‰ à¹€à¸§à¸¥à¸²] LT",lastWeek:"[à¸§à¸±à¸™]dddd[à¸—à¸µà¹ˆà¹à¸¥à¹‰à¸§ à¹€à¸§à¸¥à¸²] LT",sameElse:"L"},relativeTime:{future:"à¸­à¸µà¸ %s",past:"%sà¸—à¸µà¹ˆà¹à¸¥à¹‰à¸§",s:"à¹„à¸¡à¹ˆà¸à¸µà¹ˆà¸§à¸´à¸™à¸²à¸—à¸µ",m:"1 à¸™à¸²à¸—à¸µ",mm:"%d à¸™à¸²à¸—à¸µ",h:"1 à¸Šà¸±à¹ˆà¸§à¹‚à¸¡à¸‡",hh:"%d à¸Šà¸±à¹ˆà¸§à¹‚à¸¡à¸‡",d:"1 à¸§à¸±à¸™",dd:"%d à¸§à¸±à¸™",M:"1 à¹€à¸”à¸·à¸­à¸™",MM:"%d à¹€à¸”à¸·à¸­à¸™",y:"1 à¸›à¸µ",yy:"%d à¸›à¸µ"}})}),function(n){n(t)}(function(n){return n.lang("tl-ph",{months:"Enero_Pebrero_Marso_Abril_Mayo_Hunyo_Hulyo_Agosto_Setyembre_Oktubre_Nobyembre_Disyembre".split("_"),monthsShort:"Ene_Peb_Mar_Abr_May_Hun_Hul_Ago_Set_Okt_Nob_Dis".split("_"),weekdays:"Linggo_Lunes_Martes_Miyerkules_Huwebes_Biyernes_Sabado".split("_"),weekdaysShort:"Lin_Lun_Mar_Miy_Huw_Biy_Sab".split("_"),weekdaysMin:"Li_Lu_Ma_Mi_Hu_Bi_Sab".split("_"),longDateFormat:{LT:"HH:mm",L:"MM/D/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY LT",LLLL:"dddd, MMMM DD, YYYY LT"},calendar:{sameDay:"[Ngayon sa] LT",nextDay:"[Bukas sa] LT",nextWeek:"dddd [sa] LT",lastDay:"[Kahapon sa] LT",lastWeek:"dddd [huling linggo] LT",sameElse:"L"},relativeTime:{future:"sa loob ng %s",past:"%s ang nakalipas",s:"ilang segundo",m:"isang minuto",mm:"%d minuto",h:"isang oras",hh:"%d oras",d:"isang araw",dd:"%d araw",M:"isang buwan",MM:"%d buwan",y:"isang taon",yy:"%d taon"},ordinal:function(n){return n},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){var t={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'Ã¼ncÃ¼",4:"'Ã¼ncÃ¼",100:"'Ã¼ncÃ¼",6:"'ncÄ±",9:"'uncu",10:"'uncu",30:"'uncu",60:"'Ä±ncÄ±",90:"'Ä±ncÄ±"};return n.lang("tr",{months:"Ocak_Åžubat_Mart_Nisan_MayÄ±s_Haziran_Temmuz_AÄŸustos_EylÃ¼l_Ekim_KasÄ±m_AralÄ±k".split("_"),monthsShort:"Oca_Åžub_Mar_Nis_May_Haz_Tem_AÄŸu_Eyl_Eki_Kas_Ara".split("_"),weekdays:"Pazar_Pazartesi_SalÄ±_Ã‡arÅŸamba_PerÅŸembe_Cuma_Cumartesi".split("_"),weekdaysShort:"Paz_Pts_Sal_Ã‡ar_Per_Cum_Cts".split("_"),weekdaysMin:"Pz_Pt_Sa_Ã‡a_Pe_Cu_Ct".split("_"),longDateFormat:{LT:"HH:mm",L:"DD.MM.YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd, D MMMM YYYY LT"},calendar:{sameDay:"[bugÃ¼n saat] LT",nextDay:"[yarÄ±n saat] LT",nextWeek:"[haftaya] dddd [saat] LT",lastDay:"[dÃ¼n] LT",lastWeek:"[geÃ§en hafta] dddd [saat] LT",sameElse:"L"},relativeTime:{future:"%s sonra",past:"%s Ã¶nce",s:"birkaÃ§ saniye",m:"bir dakika",mm:"%d dakika",h:"bir saat",hh:"%d saat",d:"bir gÃ¼n",dd:"%d gÃ¼n",M:"bir ay",MM:"%d ay",y:"bir yÄ±l",yy:"%d yÄ±l"},ordinal:function(n){if(n===0)return n+"'Ä±ncÄ±";var i=n%10,r=n%100-i,u=n>=100?100:null;return n+(t[i]||t[r]||t[u])},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("tzm-la",{months:"innayr_brË¤ayrË¤_marË¤sË¤_ibrir_mayyw_ywnyw_ywlywz_É£wÅ¡t_Å¡wtanbir_ktË¤wbrË¤_nwwanbir_dwjnbir".split("_"),monthsShort:"innayr_brË¤ayrË¤_marË¤sË¤_ibrir_mayyw_ywnyw_ywlywz_É£wÅ¡t_Å¡wtanbir_ktË¤wbrË¤_nwwanbir_dwjnbir".split("_"),weekdays:"asamas_aynas_asinas_akras_akwas_asimwas_asiá¸yas".split("_"),weekdaysShort:"asamas_aynas_asinas_akras_akwas_asimwas_asiá¸yas".split("_"),weekdaysMin:"asamas_aynas_asinas_akras_akwas_asimwas_asiá¸yas".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:"[asdkh g] LT",nextDay:"[aska g] LT",nextWeek:"dddd [g] LT",lastDay:"[assant g] LT",lastWeek:"dddd [g] LT",sameElse:"L"},relativeTime:{future:"dadkh s yan %s",past:"yan %s",s:"imik",m:"minuá¸",mm:"%d minuá¸",h:"saÉ›a",hh:"%d tassaÉ›in",d:"ass",dd:"%d ossan",M:"ayowr",MM:"%d iyyirn",y:"asgas",yy:"%d isgasn"},week:{dow:6,doy:12}})}),function(n){n(t)}(function(n){return n.lang("tzm",{months:"âµ‰âµâµâ´°âµ¢âµ”_â´±âµ•â´°âµ¢âµ•_âµŽâ´°âµ•âµš_âµ‰â´±âµ”âµ‰âµ”_âµŽâ´°âµ¢âµ¢âµ“_âµ¢âµ“âµâµ¢âµ“_âµ¢âµ“âµâµ¢âµ“âµ£_âµ–âµ“âµ›âµœ_âµ›âµ“âµœâ´°âµâ´±âµ‰âµ”_â´½âµŸâµ“â´±âµ•_âµâµ“âµ¡â´°âµâ´±âµ‰âµ”_â´·âµ“âµŠâµâ´±âµ‰âµ”".split("_"),monthsShort:"âµ‰âµâµâ´°âµ¢âµ”_â´±âµ•â´°âµ¢âµ•_âµŽâ´°âµ•âµš_âµ‰â´±âµ”âµ‰âµ”_âµŽâ´°âµ¢âµ¢âµ“_âµ¢âµ“âµâµ¢âµ“_âµ¢âµ“âµâµ¢âµ“âµ£_âµ–âµ“âµ›âµœ_âµ›âµ“âµœâ´°âµâ´±âµ‰âµ”_â´½âµŸâµ“â´±âµ•_âµâµ“âµ¡â´°âµâ´±âµ‰âµ”_â´·âµ“âµŠâµâ´±âµ‰âµ”".split("_"),weekdays:"â´°âµ™â´°âµŽâ´°âµ™_â´°âµ¢âµâ´°âµ™_â´°âµ™âµ‰âµâ´°âµ™_â´°â´½âµ”â´°âµ™_â´°â´½âµ¡â´°âµ™_â´°âµ™âµ‰âµŽâµ¡â´°âµ™_â´°âµ™âµ‰â´¹âµ¢â´°âµ™".split("_"),weekdaysShort:"â´°âµ™â´°âµŽâ´°âµ™_â´°âµ¢âµâ´°âµ™_â´°âµ™âµ‰âµâ´°âµ™_â´°â´½âµ”â´°âµ™_â´°â´½âµ¡â´°âµ™_â´°âµ™âµ‰âµŽâµ¡â´°âµ™_â´°âµ™âµ‰â´¹âµ¢â´°âµ™".split("_"),weekdaysMin:"â´°âµ™â´°âµŽâ´°âµ™_â´°âµ¢âµâ´°âµ™_â´°âµ™âµ‰âµâ´°âµ™_â´°â´½âµ”â´°âµ™_â´°â´½âµ¡â´°âµ™_â´°âµ™âµ‰âµŽâµ¡â´°âµ™_â´°âµ™âµ‰â´¹âµ¢â´°âµ™".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"dddd D MMMM YYYY LT"},calendar:{sameDay:"[â´°âµ™â´·âµ… â´´] LT",nextDay:"[â´°âµ™â´½â´° â´´] LT",nextWeek:"dddd [â´´] LT",lastDay:"[â´°âµšâ´°âµâµœ â´´] LT",lastWeek:"dddd [â´´] LT",sameElse:"L"},relativeTime:{future:"â´·â´°â´·âµ… âµ™ âµ¢â´°âµ %s",past:"âµ¢â´°âµ %s",s:"âµ‰âµŽâµ‰â´½",m:"âµŽâµ‰âµâµ“â´º",mm:"%d âµŽâµ‰âµâµ“â´º",h:"âµ™â´°âµ„â´°",hh:"%d âµœâ´°âµ™âµ™â´°âµ„âµ‰âµ",d:"â´°âµ™âµ™",dd:"%d oâµ™âµ™â´°âµ",M:"â´°âµ¢oâµ“âµ”",MM:"%d âµ‰âµ¢âµ¢âµ‰âµ”âµ",y:"â´°âµ™â´³â´°âµ™",yy:"%d âµ‰âµ™â´³â´°âµ™âµ"},week:{dow:6,doy:12}})}),function(n){n(t)}(function(n){function r(n,t){var i=n.split("_");return t%10==1&&t%100!=11?i[0]:t%10>=2&&t%10<=4&&(t%100<10||t%100>=20)?i[1]:i[2]}function t(n,t,i){return i==="m"?t?"Ñ…Ð²Ð¸Ð»Ð¸Ð½Ð°":"Ñ…Ð²Ð¸Ð»Ð¸Ð½Ñƒ":i==="h"?t?"Ð³Ð¾Ð´Ð¸Ð½Ð°":"Ð³Ð¾Ð´Ð¸Ð½Ñƒ":n+" "+r({mm:"Ñ…Ð²Ð¸Ð»Ð¸Ð½Ð°_Ñ…Ð²Ð¸Ð»Ð¸Ð½Ð¸_Ñ…Ð²Ð¸Ð»Ð¸Ð½",hh:"Ð³Ð¾Ð´Ð¸Ð½Ð°_Ð³Ð¾Ð´Ð¸Ð½Ð¸_Ð³Ð¾Ð´Ð¸Ð½",dd:"Ð´ÐµÐ½ÑŒ_Ð´Ð½Ñ–_Ð´Ð½Ñ–Ð²",MM:"Ð¼Ñ–ÑÑÑ†ÑŒ_Ð¼Ñ–ÑÑÑ†Ñ–_Ð¼Ñ–ÑÑÑ†Ñ–Ð²",yy:"Ñ€Ñ–Ðº_Ñ€Ð¾ÐºÐ¸_Ñ€Ð¾ÐºÑ–Ð²"}[i],+n)}function u(n,t){var i={nominative:"ÑÑ–Ñ‡ÐµÐ½ÑŒ_Ð»ÑŽÑ‚Ð¸Ð¹_Ð±ÐµÑ€ÐµÐ·ÐµÐ½ÑŒ_ÐºÐ²Ñ–Ñ‚ÐµÐ½ÑŒ_Ñ‚Ñ€Ð°Ð²ÐµÐ½ÑŒ_Ñ‡ÐµÑ€Ð²ÐµÐ½ÑŒ_Ð»Ð¸Ð¿ÐµÐ½ÑŒ_ÑÐµÑ€Ð¿ÐµÐ½ÑŒ_Ð²ÐµÑ€ÐµÑÐµÐ½ÑŒ_Ð¶Ð¾Ð²Ñ‚ÐµÐ½ÑŒ_Ð»Ð¸ÑÑ‚Ð¾Ð¿Ð°Ð´_Ð³Ñ€ÑƒÐ´ÐµÐ½ÑŒ".split("_"),accusative:"ÑÑ–Ñ‡Ð½Ñ_Ð»ÑŽÑ‚Ð¾Ð³Ð¾_Ð±ÐµÑ€ÐµÐ·Ð½Ñ_ÐºÐ²Ñ–Ñ‚Ð½Ñ_Ñ‚Ñ€Ð°Ð²Ð½Ñ_Ñ‡ÐµÑ€Ð²Ð½Ñ_Ð»Ð¸Ð¿Ð½Ñ_ÑÐµÑ€Ð¿Ð½Ñ_Ð²ÐµÑ€ÐµÑÐ½Ñ_Ð¶Ð¾Ð²Ñ‚Ð½Ñ_Ð»Ð¸ÑÑ‚Ð¾Ð¿Ð°Ð´Ð°_Ð³Ñ€ÑƒÐ´Ð½Ñ".split("_")},r=/D[oD]? *MMMM?/.test(t)?"accusative":"nominative";return i[r][n.month()]}function f(n,t){var i={nominative:"Ð½ÐµÐ´Ñ–Ð»Ñ_Ð¿Ð¾Ð½ÐµÐ´Ñ–Ð»Ð¾Ðº_Ð²Ñ–Ð²Ñ‚Ð¾Ñ€Ð¾Ðº_ÑÐµÑ€ÐµÐ´Ð°_Ñ‡ÐµÑ‚Ð²ÐµÑ€_Ð¿â€™ÑÑ‚Ð½Ð¸Ñ†Ñ_ÑÑƒÐ±Ð¾Ñ‚Ð°".split("_"),accusative:"Ð½ÐµÐ´Ñ–Ð»ÑŽ_Ð¿Ð¾Ð½ÐµÐ´Ñ–Ð»Ð¾Ðº_Ð²Ñ–Ð²Ñ‚Ð¾Ñ€Ð¾Ðº_ÑÐµÑ€ÐµÐ´Ñƒ_Ñ‡ÐµÑ‚Ð²ÐµÑ€_Ð¿â€™ÑÑ‚Ð½Ð¸Ñ†ÑŽ_ÑÑƒÐ±Ð¾Ñ‚Ñƒ".split("_"),genitive:"Ð½ÐµÐ´Ñ–Ð»Ñ–_Ð¿Ð¾Ð½ÐµÐ´Ñ–Ð»ÐºÐ°_Ð²Ñ–Ð²Ñ‚Ð¾Ñ€ÐºÐ°_ÑÐµÑ€ÐµÐ´Ð¸_Ñ‡ÐµÑ‚Ð²ÐµÑ€Ð³Ð°_Ð¿â€™ÑÑ‚Ð½Ð¸Ñ†Ñ–_ÑÑƒÐ±Ð¾Ñ‚Ð¸".split("_")},r=/(\[[Ð’Ð²Ð£Ñƒ]\]) ?dddd/.test(t)?"accusative":/\[?(?:Ð¼Ð¸Ð½ÑƒÐ»Ð¾Ñ—|Ð½Ð°ÑÑ‚ÑƒÐ¿Ð½Ð¾Ñ—)? ?\] ?dddd/.test(t)?"genitive":"nominative";return i[r][n.day()]}function i(n){return function(){return n+"Ð¾"+(this.hours()===11?"Ð±":"")+"] LT"}}return n.lang("uk",{months:u,monthsShort:"ÑÑ–Ñ‡_Ð»ÑŽÑ‚_Ð±ÐµÑ€_ÐºÐ²Ñ–Ñ‚_Ñ‚Ñ€Ð°Ð²_Ñ‡ÐµÑ€Ð²_Ð»Ð¸Ð¿_ÑÐµÑ€Ð¿_Ð²ÐµÑ€_Ð¶Ð¾Ð²Ñ‚_Ð»Ð¸ÑÑ‚_Ð³Ñ€ÑƒÐ´".split("_"),weekdays:f,weekdaysShort:"Ð½Ð´_Ð¿Ð½_Ð²Ñ‚_ÑÑ€_Ñ‡Ñ‚_Ð¿Ñ‚_ÑÐ±".split("_"),weekdaysMin:"Ð½Ð´_Ð¿Ð½_Ð²Ñ‚_ÑÑ€_Ñ‡Ñ‚_Ð¿Ñ‚_ÑÐ±".split("_"),longDateFormat:{LT:"HH:mm",L:"DD.MM.YYYY",LL:"D MMMM YYYY Ñ€.",LLL:"D MMMM YYYY Ñ€., LT",LLLL:"dddd, D MMMM YYYY Ñ€., LT"},calendar:{sameDay:i("[Ð¡ÑŒÐ¾Ð³Ð¾Ð´Ð½Ñ– "),nextDay:i("[Ð—Ð°Ð²Ñ‚Ñ€Ð° "),lastDay:i("[Ð’Ñ‡Ð¾Ñ€Ð° "),nextWeek:i("[Ð£] dddd ["),lastWeek:function(){switch(this.day()){case 0:case 3:case 5:case 6:return i("[ÐœÐ¸Ð½ÑƒÐ»Ð¾Ñ—] dddd [").call(this);case 1:case 2:case 4:return i("[ÐœÐ¸Ð½ÑƒÐ»Ð¾Ð³Ð¾] dddd [").call(this)}},sameElse:"L"},relativeTime:{future:"Ð·Ð° %s",past:"%s Ñ‚Ð¾Ð¼Ñƒ",s:"Ð´ÐµÐºÑ–Ð»ÑŒÐºÐ° ÑÐµÐºÑƒÐ½Ð´",m:t,mm:t,h:"Ð³Ð¾Ð´Ð¸Ð½Ñƒ",hh:t,d:"Ð´ÐµÐ½ÑŒ",dd:t,M:"Ð¼Ñ–ÑÑÑ†ÑŒ",MM:t,y:"Ñ€Ñ–Ðº",yy:t},meridiem:function(n){return n<4?"Ð½Ð¾Ñ‡Ñ–":n<12?"Ñ€Ð°Ð½ÐºÑƒ":n<17?"Ð´Ð½Ñ":"Ð²ÐµÑ‡Ð¾Ñ€Ð°"},ordinal:function(n,t){switch(t){case"M":case"d":case"DDD":case"w":case"W":return n+"-Ð¹";case"D":return n+"-Ð³Ð¾";default:return n}},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("uz",{months:"ÑÐ½Ð²Ð°Ñ€ÑŒ_Ñ„ÐµÐ²Ñ€Ð°Ð»ÑŒ_Ð¼Ð°Ñ€Ñ‚_Ð°Ð¿Ñ€ÐµÐ»ÑŒ_Ð¼Ð°Ð¹_Ð¸ÑŽÐ½ÑŒ_Ð¸ÑŽÐ»ÑŒ_Ð°Ð²Ð³ÑƒÑÑ‚_ÑÐµÐ½Ñ‚ÑÐ±Ñ€ÑŒ_Ð¾ÐºÑ‚ÑÐ±Ñ€ÑŒ_Ð½Ð¾ÑÐ±Ñ€ÑŒ_Ð´ÐµÐºÐ°Ð±Ñ€ÑŒ".split("_"),monthsShort:"ÑÐ½Ð²_Ñ„ÐµÐ²_Ð¼Ð°Ñ€_Ð°Ð¿Ñ€_Ð¼Ð°Ð¹_Ð¸ÑŽÐ½_Ð¸ÑŽÐ»_Ð°Ð²Ð³_ÑÐµÐ½_Ð¾ÐºÑ‚_Ð½Ð¾Ñ_Ð´ÐµÐº".split("_"),weekdays:"Ð¯ÐºÑˆÐ°Ð½Ð±Ð°_Ð”ÑƒÑˆÐ°Ð½Ð±Ð°_Ð¡ÐµÑˆÐ°Ð½Ð±Ð°_Ð§Ð¾Ñ€ÑˆÐ°Ð½Ð±Ð°_ÐŸÐ°Ð¹ÑˆÐ°Ð½Ð±Ð°_Ð–ÑƒÐ¼Ð°_Ð¨Ð°Ð½Ð±Ð°".split("_"),weekdaysShort:"Ð¯ÐºÑˆ_Ð”ÑƒÑˆ_Ð¡ÐµÑˆ_Ð§Ð¾Ñ€_ÐŸÐ°Ð¹_Ð–ÑƒÐ¼_Ð¨Ð°Ð½".split("_"),weekdaysMin:"Ð¯Ðº_Ð”Ñƒ_Ð¡Ðµ_Ð§Ð¾_ÐŸÐ°_Ð–Ñƒ_Ð¨Ð°".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY LT",LLLL:"D MMMM YYYY, dddd LT"},calendar:{sameDay:"[Ð‘ÑƒÐ³ÑƒÐ½ ÑÐ¾Ð°Ñ‚] LT [Ð´Ð°]",nextDay:"[Ð­Ñ€Ñ‚Ð°Ð³Ð°] LT [Ð´Ð°]",nextWeek:"dddd [ÐºÑƒÐ½Ð¸ ÑÐ¾Ð°Ñ‚] LT [Ð´Ð°]",lastDay:"[ÐšÐµÑ‡Ð° ÑÐ¾Ð°Ñ‚] LT [Ð´Ð°]",lastWeek:"[Ð£Ñ‚Ð³Ð°Ð½] dddd [ÐºÑƒÐ½Ð¸ ÑÐ¾Ð°Ñ‚] LT [Ð´Ð°]",sameElse:"L"},relativeTime:{future:"Ð¯ÐºÐ¸Ð½ %s Ð¸Ñ‡Ð¸Ð´Ð°",past:"Ð‘Ð¸Ñ€ Ð½ÐµÑ‡Ð° %s Ð¾Ð»Ð´Ð¸Ð½",s:"Ñ„ÑƒÑ€ÑÐ°Ñ‚",m:"Ð±Ð¸Ñ€ Ð´Ð°ÐºÐ¸ÐºÐ°",mm:"%d Ð´Ð°ÐºÐ¸ÐºÐ°",h:"Ð±Ð¸Ñ€ ÑÐ¾Ð°Ñ‚",hh:"%d ÑÐ¾Ð°Ñ‚",d:"Ð±Ð¸Ñ€ ÐºÑƒÐ½",dd:"%d ÐºÑƒÐ½",M:"Ð±Ð¸Ñ€ Ð¾Ð¹",MM:"%d Ð¾Ð¹",y:"Ð±Ð¸Ñ€ Ð¹Ð¸Ð»",yy:"%d Ð¹Ð¸Ð»"},week:{dow:1,doy:7}})}),function(n){n(t)}(function(n){return n.lang("vi",{months:"thÃ¡ng 1_thÃ¡ng 2_thÃ¡ng 3_thÃ¡ng 4_thÃ¡ng 5_thÃ¡ng 6_thÃ¡ng 7_thÃ¡ng 8_thÃ¡ng 9_thÃ¡ng 10_thÃ¡ng 11_thÃ¡ng 12".split("_"),monthsShort:"Th01_Th02_Th03_Th04_Th05_Th06_Th07_Th08_Th09_Th10_Th11_Th12".split("_"),weekdays:"chá»§ nháº­t_thá»© hai_thá»© ba_thá»© tÆ°_thá»© nÄƒm_thá»© sÃ¡u_thá»© báº£y".split("_"),weekdaysShort:"CN_T2_T3_T4_T5_T6_T7".split("_"),weekdaysMin:"CN_T2_T3_T4_T5_T6_T7".split("_"),longDateFormat:{LT:"HH:mm",L:"DD/MM/YYYY",LL:"D MMMM [nÄƒm] YYYY",LLL:"D MMMM [nÄƒm] YYYY LT",LLLL:"dddd, D MMMM [nÄƒm] YYYY LT",l:"DD/M/YYYY",ll:"D MMM YYYY",lll:"D MMM YYYY LT",llll:"ddd, D MMM YYYY LT"},calendar:{sameDay:"[HÃ´m nay lÃºc] LT",nextDay:"[NgÃ y mai lÃºc] LT",nextWeek:"dddd [tuáº§n tá»›i lÃºc] LT",lastDay:"[HÃ´m qua lÃºc] LT",lastWeek:"dddd [tuáº§n rá»“i lÃºc] LT",sameElse:"L"},relativeTime:{future:"%s tá»›i",past:"%s trÆ°á»›c",s:"vÃ i giÃ¢y",m:"má»™t phÃºt",mm:"%d phÃºt",h:"má»™t giá»",hh:"%d giá»",d:"má»™t ngÃ y",dd:"%d ngÃ y",M:"má»™t thÃ¡ng",MM:"%d thÃ¡ng",y:"má»™t nÄƒm",yy:"%d nÄƒm"},ordinal:function(n){return n},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("zh-cn",{months:"ä¸€æœˆ_äºŒæœˆ_ä¸‰æœˆ_å››æœˆ_äº”æœˆ_å…­æœˆ_ä¸ƒæœˆ_å…«æœˆ_ä¹æœˆ_åæœˆ_åä¸€æœˆ_åäºŒæœˆ".split("_"),monthsShort:"1æœˆ_2æœˆ_3æœˆ_4æœˆ_5æœˆ_6æœˆ_7æœˆ_8æœˆ_9æœˆ_10æœˆ_11æœˆ_12æœˆ".split("_"),weekdays:"æ˜ŸæœŸæ—¥_æ˜ŸæœŸä¸€_æ˜ŸæœŸäºŒ_æ˜ŸæœŸä¸‰_æ˜ŸæœŸå››_æ˜ŸæœŸäº”_æ˜ŸæœŸå…­".split("_"),weekdaysShort:"å‘¨æ—¥_å‘¨ä¸€_å‘¨äºŒ_å‘¨ä¸‰_å‘¨å››_å‘¨äº”_å‘¨å…­".split("_"),weekdaysMin:"æ—¥_ä¸€_äºŒ_ä¸‰_å››_äº”_å…­".split("_"),longDateFormat:{LT:"Ahç‚¹mm",L:"YYYY-MM-DD",LL:"YYYYå¹´MMMDæ—¥",LLL:"YYYYå¹´MMMDæ—¥LT",LLLL:"YYYYå¹´MMMDæ—¥ddddLT",l:"YYYY-MM-DD",ll:"YYYYå¹´MMMDæ—¥",lll:"YYYYå¹´MMMDæ—¥LT",llll:"YYYYå¹´MMMDæ—¥ddddLT"},meridiem:function(n,t){var i=n*100+t;return i<600?"å‡Œæ™¨":i<900?"æ—©ä¸Š":i<1130?"ä¸Šåˆ":i<1230?"ä¸­åˆ":i<1800?"ä¸‹åˆ":"æ™šä¸Š"},calendar:{sameDay:function(){return this.minutes()===0?"[ä»Šå¤©]Ah[ç‚¹æ•´]":"[ä»Šå¤©]LT"},nextDay:function(){return this.minutes()===0?"[æ˜Žå¤©]Ah[ç‚¹æ•´]":"[æ˜Žå¤©]LT"},lastDay:function(){return this.minutes()===0?"[æ˜¨å¤©]Ah[ç‚¹æ•´]":"[æ˜¨å¤©]LT"},nextWeek:function(){var i,t;return i=n().startOf("week"),t=this.unix()-i.unix()>=604800?"[ä¸‹]":"[æœ¬]",this.minutes()===0?t+"dddAhç‚¹æ•´":t+"dddAhç‚¹mm"},lastWeek:function(){var i,t;return i=n().startOf("week"),t=this.unix()<i.unix()?"[ä¸Š]":"[æœ¬]",this.minutes()===0?t+"dddAhç‚¹æ•´":t+"dddAhç‚¹mm"},sameElse:"LL"},ordinal:function(n,t){switch(t){case"d":case"D":case"DDD":return n+"æ—¥";case"M":return n+"æœˆ";case"w":case"W":return n+"å‘¨";default:return n}},relativeTime:{future:"%så†…",past:"%så‰",s:"å‡ ç§’",m:"1åˆ†é’Ÿ",mm:"%dåˆ†é’Ÿ",h:"1å°æ—¶",hh:"%då°æ—¶",d:"1å¤©",dd:"%då¤©",M:"1ä¸ªæœˆ",MM:"%dä¸ªæœˆ",y:"1å¹´",yy:"%då¹´"},week:{dow:1,doy:4}})}),function(n){n(t)}(function(n){return n.lang("zh-tw",{months:"ä¸€æœˆ_äºŒæœˆ_ä¸‰æœˆ_å››æœˆ_äº”æœˆ_å…­æœˆ_ä¸ƒæœˆ_å…«æœˆ_ä¹æœˆ_åæœˆ_åä¸€æœˆ_åäºŒæœˆ".split("_"),monthsShort:"1æœˆ_2æœˆ_3æœˆ_4æœˆ_5æœˆ_6æœˆ_7æœˆ_8æœˆ_9æœˆ_10æœˆ_11æœˆ_12æœˆ".split("_"),weekdays:"æ˜ŸæœŸæ—¥_æ˜ŸæœŸä¸€_æ˜ŸæœŸäºŒ_æ˜ŸæœŸä¸‰_æ˜ŸæœŸå››_æ˜ŸæœŸäº”_æ˜ŸæœŸå…­".split("_"),weekdaysShort:"é€±æ—¥_é€±ä¸€_é€±äºŒ_é€±ä¸‰_é€±å››_é€±äº”_é€±å…­".split("_"),weekdaysMin:"æ—¥_ä¸€_äºŒ_ä¸‰_å››_äº”_å…­".split("_"),longDateFormat:{LT:"Ahé»žmm",L:"YYYYå¹´MMMDæ—¥",LL:"YYYYå¹´MMMDæ—¥",LLL:"YYYYå¹´MMMDæ—¥LT",LLLL:"YYYYå¹´MMMDæ—¥ddddLT",l:"YYYYå¹´MMMDæ—¥",ll:"YYYYå¹´MMMDæ—¥",lll:"YYYYå¹´MMMDæ—¥LT",llll:"YYYYå¹´MMMDæ—¥ddddLT"},meridiem:function(n,t){var i=n*100+t;return i<900?"æ—©ä¸Š":i<1130?"ä¸Šåˆ":i<1230?"ä¸­åˆ":i<1800?"ä¸‹åˆ":"æ™šä¸Š"},calendar:{sameDay:"[ä»Šå¤©]LT",nextDay:"[æ˜Žå¤©]LT",nextWeek:"[ä¸‹]ddddLT",lastDay:"[æ˜¨å¤©]LT",lastWeek:"[ä¸Š]ddddLT",sameElse:"L"},ordinal:function(n,t){switch(t){case"d":case"D":case"DDD":return n+"æ—¥";case"M":return n+"æœˆ";case"w":case"W":return n+"é€±";default:return n}},relativeTime:{future:"%så…§",past:"%så‰",s:"å¹¾ç§’",m:"ä¸€åˆ†é˜",mm:"%dåˆ†é˜",h:"ä¸€å°æ™‚",hh:"%då°æ™‚",d:"ä¸€å¤©",dd:"%då¤©",M:"ä¸€å€‹æœˆ",MM:"%då€‹æœˆ",y:"ä¸€å¹´",yy:"%då¹´"}})});t.lang("en");ii?module.exports=t:typeof define=="function"&&define.amd?(define("moment",function(n,i,r){return r.config&&r.config()&&r.config().noGlobal===!0&&(nt.moment=ni),t}),or(!0)):or()}).call(this);
